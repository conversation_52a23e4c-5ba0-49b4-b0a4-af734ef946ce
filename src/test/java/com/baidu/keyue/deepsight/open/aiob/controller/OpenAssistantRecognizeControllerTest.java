package com.baidu.keyue.deepsight.open.aiob.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.open.aiob.model.dto.OpenAssistantRecDTO;
import com.baidu.keyue.deepsight.open.aiob.model.vo.OpenAssistantRecVO;
import com.baidu.keyue.deepsight.open.aiob.service.OpenAssistantRecognizeService;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

public class OpenAssistantRecognizeControllerTest{

    private final ObjectMapper objectMapper = new ObjectMapper();

    private MockMvc mockMvc;

    @Mock
    private OpenAssistantRecognizeService openAssistantRecognizeService;

    @InjectMocks
    private OpenAssistantRecognizeController openAssistantRecognizeController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(openAssistantRecognizeController).build();
    }

    @Test
    void recognizeAssistantSuccess() throws Exception {
        String tenantId = "testTenant";
        OpenAssistantRecDTO request = new OpenAssistantRecDTO();
        request.setTenantId(tenantId);
    
        OpenAssistantRecVO mockResponse = OpenAssistantRecVO.builder()
                .assistantCount(5)
                .prepareSessionCount(3)
                .build();
    
        when(openAssistantRecognizeService.recognizeAssistant(eq(tenantId), any())).thenReturn(mockResponse);
    
        String requestBody = objectMapper.writeValueAsString(request);
    
        mockMvc.perform(post("/open/deepsight/v1/aiob/assistant/recognize")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ErrorCode.SUCCESS.getCode()))
                .andExpect(jsonPath("$.data.assistantCount").value(5))
                .andExpect(jsonPath("$.data.prepareSessionCount").value(3));
    
        verify(openAssistantRecognizeService, times(1)).recognizeAssistant(eq(tenantId), any());
    }

    @Test
    void recognizeAssistantEmptyTenantId() throws Exception {
        OpenAssistantRecDTO request = new OpenAssistantRecDTO();
        request.setTenantId(null);
    
        String requestBody = objectMapper.writeValueAsString(request);
    
        mockMvc.perform(post("/open/deepsight/v1/aiob/assistant/recognize")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isBadRequest());
    
        verify(openAssistantRecognizeService, never()).recognizeAssistant(any(), any());
    }

}