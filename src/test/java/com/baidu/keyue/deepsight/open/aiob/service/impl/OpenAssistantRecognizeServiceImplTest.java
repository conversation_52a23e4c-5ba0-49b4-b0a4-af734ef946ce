package com.baidu.keyue.deepsight.open.aiob.service.impl;

import com.baidu.keyue.deepsight.config.AiobMetricAggConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.open.aiob.model.vo.OpenAssistantRecVO;
import com.baidu.keyue.deepsight.open.aiob.service.SessionAggKafkaMsgService;
import com.baidu.keyue.deepsight.service.ai.AssistantRecognitionService;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class OpenAssistantRecognizeServiceImplTest {

    @Mock
    private DorisService dorisService;

    @Mock
    private AssistantRecognitionService assistantRecognitionService;

    @Mock
    private SessionAggKafkaMsgService kafkaMessageService;

    @Mock
    private AiobMetricAggConfiguration aiobAggConfiguration;

    @InjectMocks
    private OpenAssistantRecognizeServiceImpl openAssistantRecognizeService;

    private String testTenantId;
    private List<Map<String, Object>> mockSessionData;

    @BeforeEach
    void setUp() {
        testTenantId = "test_tenant_id";

        // Mock配置
        when(aiobAggConfiguration.getDay()).thenReturn(90);

        // 准备测试数据
        mockSessionData = new ArrayList<>();
        
        Map<String, Object> session1 = new HashMap<>();
        session1.put("sessionId", "session_001");
        session1.put("taskId", "task_001");
        session1.put("conversationContent", "用户：你好\n机器人：您好，请问有什么可以帮助您的？");
        mockSessionData.add(session1);
        
        Map<String, Object> session2 = new HashMap<>();
        session2.put("sessionId", "session_002");
        session2.put("taskId", "task_002");
        session2.put("conversationContent", "用户：我想了解产品信息\n机器人：好的，我来为您介绍");
        mockSessionData.add(session2);
    }

    @Test
    void testRecognizeAssistant_Success() {
        // Mock ORMUtils.generateSessionContentQueryForAssistant
        try (MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            String mockSql = "SELECT sessionId, taskId, conversationContent FROM aiob_conversation_session_service_test_tenant_id WHERE talkingTurn >= 2 AND sipCode = '200' AND isRobotHangup = true AND is_auto_answer IS NULL AND conversationContent IS NOT NULL AND conversationContent != ''";
            mockedORMUtils.when(() -> ORMUtils.generateSessionContentQueryForAssistant(eq(testTenantId), any(LocalDate.class)))
                    .thenReturn(mockSql);

            // Mock batch update SQL generation
            String mockUpdateSql1 = "UPDATE aiob_conversation_session_service_test_tenant_id SET is_auto_answer = 1 WHERE sessionId IN ('session_001')";
            String mockUpdateSql2 = "UPDATE aiob_conversation_session_service_test_tenant_id SET is_auto_answer = 0 WHERE sessionId IN ('session_002')";
            mockedORMUtils.when(() -> ORMUtils.generateBatchUpdateAutoAnswerSQL(anyString(), anyList(), eq(1)))
                    .thenReturn(mockUpdateSql1);
            mockedORMUtils.when(() -> ORMUtils.generateBatchUpdateAutoAnswerSQL(anyString(), anyList(), eq(0)))
                    .thenReturn(mockUpdateSql2);

            // Mock dorisService.selectList
            when(dorisService.selectList(mockSql)).thenReturn(mockSessionData);

            // Mock assistantRecognitionService.recognizeAssistant
            when(assistantRecognitionService.recognizeAssistant(eq("session_001"), eq("task_001"), anyString(), eq(testTenantId)))
                    .thenReturn(true);
            when(assistantRecognitionService.recognizeAssistant(eq("session_002"), eq("task_002"), anyString(), eq(testTenantId)))
                    .thenReturn(false);

            // 执行测试
            LocalDate testStartDate = LocalDate.now().minusDays(30);
            OpenAssistantRecVO result = openAssistantRecognizeService.recognizeAssistant(testTenantId, testStartDate);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.getPrepareSessionCount());
            assertEquals(1, result.getAssistantCount());

            // 验证方法调用
            verify(dorisService, times(1)).selectList(mockSql);
            verify(assistantRecognitionService, times(2)).recognizeAssistant(anyString(), anyString(), anyString(), eq(testTenantId));
            // 验证数据库更新被调用（现在所有session都会更新）
            verify(dorisService, times(2)).execSql(anyString());
            // 验证Kafka消息发送被调用（现在所有session都会发送）
            verify(kafkaMessageService, times(1)).sendBatchMessages(anyList(), eq(testTenantId));
        }
    }

    @Test
    void testRecognizeAssistant_EmptyResult() {
        // Mock ORMUtils.generateSessionContentQueryForAssistant
        try (MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            String mockSql = "SELECT sessionId, taskId, conversationContent FROM aiob_conversation_session_service_test_tenant_id WHERE talkingTurn >= 2 AND sipCode = '200' AND isRobotHangup = true AND is_auto_answer IS NULL AND conversationContent IS NOT NULL AND conversationContent != ''";
            mockedORMUtils.when(() -> ORMUtils.generateSessionContentQueryForAssistant(eq(testTenantId), any(LocalDate.class)))
                    .thenReturn(mockSql);

            // Mock dorisService.selectList 返回空列表
            when(dorisService.selectList(mockSql)).thenReturn(new ArrayList<>());

            // 执行测试
            LocalDate testStartDate = LocalDate.now().minusDays(30);
            OpenAssistantRecVO result = openAssistantRecognizeService.recognizeAssistant(testTenantId, testStartDate);

            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.getPrepareSessionCount());
            assertEquals(0, result.getAssistantCount());

            // 验证方法调用
            verify(dorisService, times(1)).selectList(mockSql);
            verify(assistantRecognitionService, never()).recognizeAssistant(anyString(), anyString(), anyString(), anyString());
            // 验证没有数据库更新和Kafka发送
            verify(dorisService, never()).execSql(anyString());
            verify(kafkaMessageService, never()).sendBatchMessages(anyList(), anyString());
        }
    }

    @Test
    void testRecognizeAssistant_DatabaseException() {
        // Mock ORMUtils.generateSessionContentQueryForAssistant
        try (MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            String mockSql = "SELECT sessionId, taskId, conversationContent FROM aiob_conversation_session_service_test_tenant_id WHERE talkingTurn >= 2 AND sipCode = '200' AND isRobotHangup = true AND is_auto_answer IS NULL AND conversationContent IS NOT NULL AND conversationContent != ''";
            mockedORMUtils.when(() -> ORMUtils.generateSessionContentQueryForAssistant(eq(testTenantId), any(LocalDate.class)))
                    .thenReturn(mockSql);

            // Mock dorisService.selectList 抛出异常
            when(dorisService.selectList(mockSql)).thenThrow(new RuntimeException("Database connection failed"));

            // 执行测试并验证异常
            LocalDate testStartDate = LocalDate.now().minusDays(30);
            DeepSightException.DorisExecException exception = assertThrows(DeepSightException.DorisExecException.class, () -> {
                openAssistantRecognizeService.recognizeAssistant(testTenantId, testStartDate);
            });

            assertTrue(exception.getMessage().contains("query session failed"));
        }
    }

    @Test
    void testRecognizeAssistant_PartialFailure() {
        // Mock ORMUtils.generateSessionContentQueryForAssistant
        try (MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            String mockSql = "SELECT sessionId, taskId, conversationContent FROM aiob_conversation_session_service_test_tenant_id WHERE talkingTurn >= 2 AND sipCode = '200' AND isRobotHangup = true AND is_auto_answer IS NULL AND conversationContent IS NOT NULL AND conversationContent != ''";
            mockedORMUtils.when(() -> ORMUtils.generateSessionContentQueryForAssistant(eq(testTenantId), any(LocalDate.class)))
                    .thenReturn(mockSql);

            // Mock batch update SQL generation
            String mockUpdateSql1 = "UPDATE aiob_conversation_session_service_test_tenant_id SET is_auto_answer = 1 WHERE sessionId IN ('session_001')";
            mockedORMUtils.when(() -> ORMUtils.generateBatchUpdateAutoAnswerSQL(anyString(), anyList(), eq(1)))
                    .thenReturn(mockUpdateSql1);

            // Mock dorisService.selectList
            when(dorisService.selectList(mockSql)).thenReturn(mockSessionData);

            // Mock assistantRecognitionService.recognizeAssistant - 第一个成功，第二个抛异常
            when(assistantRecognitionService.recognizeAssistant(eq("session_001"), eq("task_001"), anyString(), eq(testTenantId)))
                    .thenReturn(true);
            when(assistantRecognitionService.recognizeAssistant(eq("session_002"), eq("task_002"), anyString(), eq(testTenantId)))
                    .thenThrow(new RuntimeException("Recognition service failed"));

            // 执行测试 - 应该不抛异常，继续处理
            LocalDate testStartDate = LocalDate.now().minusDays(30);
            OpenAssistantRecVO result = openAssistantRecognizeService.recognizeAssistant(testTenantId, testStartDate);

            // 验证结果 - 只有第一个成功处理
            assertNotNull(result);
            assertEquals(2, result.getPrepareSessionCount());
            assertEquals(1, result.getAssistantCount());

            // 验证数据库更新被调用（第一个session成功处理）
            verify(dorisService, times(1)).execSql(anyString());
            // 验证Kafka消息发送被调用（第一个session成功处理）
            verify(kafkaMessageService, times(1)).sendBatchMessages(anyList(), eq(testTenantId));
        }
    }
}
