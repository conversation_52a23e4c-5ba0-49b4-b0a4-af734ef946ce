package com.baidu.keyue.deepsight.service.ai;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.service.ai.impl.AssistantRecognitionServiceImpl;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.kybase.commons.utils.HttpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AssistantRecognitionService 测试类
 */
@ExtendWith(MockitoExtension.class)
class AssistantRecognitionServiceImplTest {

    @Mock
    private DorisService dorisService;

    @InjectMocks
    private AssistantRecognitionServiceImpl assistantRecognitionService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(assistantRecognitionService, "assistantRecognitionUrl",
                "http://10.234.52.167:8097/getAssistantRecognition/v1");
        ReflectionTestUtils.setField(assistantRecognitionService, "timeout", 5000);
        ReflectionTestUtils.setField(assistantRecognitionService, "objectMapper", objectMapper);
    }

    @Test
    void testRecognizeAssistant_Success() {
        // 准备测试数据
        String sessionId = "test_session_id";
        String taskId = "test_task_id";
        String conversationContent = "AI: 喂，您好\n用户: 我是通话助理。\nAI: 好的，谢谢。";
        String tenantId = "test_tenant_id";

        // Mock nodeInfo数据 (Base64编码的JSON)
        Map<String, Object> nodeData = new HashMap<>();
        nodeData.put("systemEvent", "hungUp");
        nodeData.put("hungUpType", 6);

        String nodeInfoJson = "{\"systemEvent\":\"hungUp\",\"hungUpType\":6}";
        String encodedNodeInfo = Base64.getEncoder().encodeToString(nodeInfoJson.getBytes());

        Map<String, Object> recordData = new HashMap<>();
        recordData.put("nodeInfo", encodedNodeInfo);

        // Mock 数据库查询
        try (MockedStatic<TenantUtils> mockedTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {

            mockedTenantUtils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId))
                    .thenReturn("aiob_conversation_record_" + tenantId);

            mockedORMUtils.when(() -> ORMUtils.generateQueryRecordNodeInfoForHangupSQL(anyString(), anyString()))
                    .thenReturn("SELECT nodeInfo FROM aiob_conversation_record_test_tenant_id WHERE sessionId = 'test_session_id' AND nodeInfo IS NOT NULL AND nodeInfo != '' AND json_extract_string(nodeInfo, '$.systemEvent') = 'hungUp' AND json_extract_int(nodeInfo, '$.hungUpType') IN (3, 6) ORDER BY createTime DESC LIMIT 1");

            when(dorisService.selectList(anyString())).thenReturn(List.of(recordData));

            // Mock 算法接口调用
            Map<String, Object> results = new HashMap<>();
            results.put("is_smart_assistant", true);
            results.put("reason", "用户发言中出现了提示性语句，符合智能小助手特征");

            Map<String, Object> apiResponse = new HashMap<>();
            apiResponse.put("status", "success");
            apiResponse.put("message", "OK");
            apiResponse.put("results", results);

            String responseJson = JsonUtils.toJsonWithOutException(apiResponse);

            try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
                mockedHttpUtil.when(() -> HttpUtil.postJsonWithTry(anyString(), anyString()))
                        .thenReturn(responseJson);

                // 执行测试
                Boolean result = assistantRecognitionService.recognizeAssistant(sessionId, taskId, conversationContent, tenantId);

                // 验证结果
                assertTrue(result);
                verify(dorisService).selectList(anyString());
                mockedHttpUtil.verify(() -> HttpUtil.postJsonWithTry(anyString(), anyString()));
            }
        }
    }

    @Test
    void testRecognizeAssistant_PreconditionNotMet() {
        // 准备测试数据
        String sessionId = "test_session_id";
        String taskId = "test_task_id";
        String conversationContent = "AI: 喂，您好\n用户: 你好。";
        String tenantId = "test_tenant_id";

        // Mock 空的record数据（不满足挂断条件）
        try (MockedStatic<TenantUtils> mockedTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {

            mockedTenantUtils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId))
                    .thenReturn("aiob_conversation_record_" + tenantId);

            mockedORMUtils.when(() -> ORMUtils.generateQueryRecordNodeInfoForHangupSQL(anyString(), anyString()))
                    .thenReturn("SELECT nodeInfo FROM aiob_conversation_record_test_tenant_id WHERE sessionId = 'test_session_id' AND nodeInfo IS NOT NULL AND nodeInfo != '' AND json_extract_string(nodeInfo, '$.systemEvent') = 'hungUp' AND json_extract_int(nodeInfo, '$.hungUpType') = 6 ORDER BY createTime DESC LIMIT 1");

            when(dorisService.selectList(anyString())).thenReturn(List.of());

            // 执行测试
            Boolean result = assistantRecognitionService.recognizeAssistant(sessionId, taskId, conversationContent, tenantId);

            // 验证结果
            assertFalse(result);
            verify(dorisService).selectList(anyString());
        }
    }

    @Test
    void testRecognizeAssistant_ApiCallFailed() {
        // 准备测试数据
        String sessionId = "test_session_id";
        String taskId = "test_task_id";
        String conversationContent = "AI: 喂，您好\n用户: 我是通话助理。";
        String tenantId = "test_tenant_id";

        // Mock nodeInfo数据
        String nodeInfoJson = "{\"systemEvent\":\"hungUp\",\"hungUpType\":6}";
        String encodedNodeInfo = Base64.getEncoder().encodeToString(nodeInfoJson.getBytes());

        Map<String, Object> recordData = new HashMap<>();
        recordData.put("nodeInfo", encodedNodeInfo);

        try (MockedStatic<TenantUtils> mockedTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {

            mockedTenantUtils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId))
                    .thenReturn("aiob_conversation_record_" + tenantId);

            mockedORMUtils.when(() -> ORMUtils.generateQueryRecordNodeInfoForHangupSQL(anyString(), anyString()))
                    .thenReturn("SELECT nodeInfo FROM aiob_conversation_record_test_tenant_id WHERE sessionId = 'test_session_id' AND nodeInfo IS NOT NULL AND nodeInfo != '' AND json_extract_string(nodeInfo, '$.systemEvent') = 'hungUp' AND json_extract_int(nodeInfo, '$.hungUpType') = 6 ORDER BY createTime DESC LIMIT 1");

            when(dorisService.selectList(anyString())).thenReturn(List.of(recordData));

            try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
                // Mock 算法接口调用失败
                mockedHttpUtil.when(() -> HttpUtil.postJsonWithTry(anyString(), anyString()))
                        .thenThrow(new RuntimeException("API call failed"));

                // 执行测试
                Boolean result = assistantRecognitionService.recognizeAssistant(sessionId, taskId, conversationContent, tenantId);

                // 验证结果
                assertNull(result);
                verify(dorisService).selectList(anyString());
                mockedHttpUtil.verify(() -> HttpUtil.postJsonWithTry(anyString(), anyString()));
            }
        }
    }



    @Test
    void testParseRecognitionResult_SuccessWithTrue() {
        // 准备测试数据 - 成功响应，识别为小秘书
        Map<String, Object> results = new HashMap<>();
        results.put("is_smart_assistant", true);
        results.put("reason", "用户发言中出现了提示性语句");

        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "OK");
        response.put("results", results);

        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                assistantRecognitionService, "parseRecognitionResult", response);

        assertTrue(result);
    }

    @Test
    void testParseRecognitionResult_SuccessWithFalse() {
        // 准备测试数据 - 成功响应，识别为非小秘书
        Map<String, Object> results = new HashMap<>();
        results.put("is_smart_assistant", false);
        results.put("reason", "用户发言正常，非智能助手");

        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "OK");
        response.put("results", results);

        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                assistantRecognitionService, "parseRecognitionResult", response);

        assertFalse(result);
    }

    @Test
    void testParseRecognitionResult_FailedStatus() {
        // 准备测试数据 - 失败响应
        Map<String, Object> response = new HashMap<>();
        response.put("status", "failed");
        response.put("message", "算法处理失败");

        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                assistantRecognitionService, "parseRecognitionResult", response);

        assertNull(result);
    }

    @Test
    void testParseRecognitionResult_StringBooleanValue() {
        // 准备测试数据 - 字符串类型的布尔值
        Map<String, Object> results = new HashMap<>();
        results.put("is_smart_assistant", "True");
        results.put("reason", "Python返回的字符串布尔值");

        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "OK");
        response.put("results", results);

        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                assistantRecognitionService, "parseRecognitionResult", response);

        assertTrue(result);
    }

    @Test
    void testParseRecognitionResult_MissingResults() {
        // 准备测试数据 - 缺少results字段
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "OK");

        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                assistantRecognitionService, "parseRecognitionResult", response);

        assertNull(result);
    }
}