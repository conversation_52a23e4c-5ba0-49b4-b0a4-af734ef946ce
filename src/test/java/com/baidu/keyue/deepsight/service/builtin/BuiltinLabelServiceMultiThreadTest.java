package com.baidu.keyue.deepsight.service.builtin;

import com.baidu.keyue.deepsight.schedule.MetricAggScheduler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 内置标签服务多线程测试
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class BuiltinLabelServiceMultiThreadTest {

//    @Resource
//    private BuiltinLabelService builtinLabelService;

    @Resource
    private MetricAggScheduler metricAggScheduler;
//
//    /**
//     * 测试多线程处理逻辑
//     *
//     * <AUTHOR>
//     */
//    @Test
//    public void testMultiThreadProcessing() {
//        log.info("testMultiThreadProcessing start");
//        List<TenantInfo> tenantInfos = new ArrayList<>();
//        TenantInfo tenantInfo = new TenantInfo();
//        tenantInfo.setTenantid("38677777407661056");
//        tenantInfos.add(tenantInfo);
//        builtinLabelService.triggerBuiltinLabelCalculationAsync(tenantInfos);
//        log.info("testMultiThreadProcessing end");
//    }

    @Test
    public void testAiobSessionMetricAgg() {
        metricAggScheduler.aiobSessionMetricAgg();
    }
}
