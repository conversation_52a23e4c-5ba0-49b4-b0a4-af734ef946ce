[{"fieldTag": 1, "isFilterCriteria": true, "isRequired": true, "cnField": "对话唯一id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "EvtSequenceNumber", "description": "每次回复的唯一表示，一行代表一次回复", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 1}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "通话id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "sessionId", "description": "一通电话的唯一表示", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 2}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "对话角色", "dataType": "<PERSON><PERSON><PERSON>", "enField": "roleType", "description": "当前对话者角色", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "enum", "dataTableId": 3, "fieldType": "string", "configInfos": [{"key": "voice", "value": "客户侧", "desc": "客户侧"}, {"key": "speech", "value": "坐席侧", "desc": "坐席侧"}], "number": 3}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "机器人id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "robotId", "description": "a527cba8-ac9b-4616-9ae1-abffd174b848", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 4}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "任务id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "taskId", "description": "1280387162636280", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 5}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "文本内容", "dataType": "text", "enField": "content", "description": "对话语音对应的文字内容", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 6}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "回复文本", "dataType": "text", "enField": "contextText", "description": "机器人侧回复文本", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 7}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "创建时间", "dataType": "datetime", "enField": "createTime", "description": "对话发生时间", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "time", "dataTableId": 3, "fieldType": "time", "number": 8}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "静默时间", "dataType": "boolean", "enField": "silent", "description": "对话者停顿、静音的时间", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "number", "dataTableId": 3, "fieldType": "number", "number": 9}, {"fieldTag": 3, "isFilterCriteria": true, "isRequired": false, "cnField": "总时长", "dataType": "int", "enField": "timeLen", "description": "单位，秒", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "number", "dataTableId": 3, "fieldType": "number", "number": 10}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "意图", "dataType": "<PERSON><PERSON><PERSON>", "enField": "intent", "description": "对话内容涉及的用户意图", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 11}, {"fieldTag": 0, "isFilterCriteria": false, "isRequired": false, "cnField": "信息收集", "dataType": "json", "enField": "collectInfo", "description": "信息收集内容", "updateTime": 1739870638260, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1739870638260, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 12}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "全局oneId", "dataType": "<PERSON><PERSON><PERSON>", "enField": "oneId", "description": "全局oneId", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 13}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "写入时间", "dataType": "datetime", "enField": "deepsight_datetime", "description": "写入时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 3, "fieldType": "time", "number": 14}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "更新时间", "dataType": "datetime", "enField": "deepsight_update_datetime", "description": "更新时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 3, "fieldType": "time", "number": 15}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "对话相对时间", "dataType": "<PERSON><PERSON><PERSON>", "enField": "start", "description": "对话相对时间", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1737173264000, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 16}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "debug查询ID", "dataType": "<PERSON><PERSON><PERSON>", "enField": "queryId", "description": "debug查询ID", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1737173264000, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 17}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "节点信息", "dataType": "json", "enField": "nodeInfo", "description": "节点信息(JSON格式)", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_aiob_talk", "isSecrete": false, "createTime": 1737173264000, "valueType": "text", "dataTableId": 3, "fieldType": "string", "number": 18}]