ALTER TABLE `global_default_aiob_conversation_record_info` ADD COLUMN `nodeInfo` JSON NULL COMMENT '节点信息';
CREATE TABLE aiob_sop_analysis_result^& (
    taskId        VARCHAR(255) NOT NULL DEFAULT '' COMMENT '任务ID',
    robotId       VARCHAR(255) NOT NULL DEFAULT '' COMMENT '机器人ID',
    robotVer      VARCHAR(255) NOT NULL DEFAULT '' COMMENT '机器人版本',
    topicId       VARCHAR(255) NOT NULL DEFAULT '' COMMENT '主题ID',
    nodeId        VARCHAR(255) NOT NULL DEFAULT '' COMMENT '节点ID',
    knowledgeType TINYINT      NOT NULL DEFAULT 0 COMMENT '1意图,2faq,3意向标签,4文档,5其他',
    knowledgeText VARCHAR(255) NOT NULL DEFAULT '' COMMENT '知识内容',

    queryId        VARCHAR(255) NULL COMMENT 'queryId',
    sessionId      VARCHAR(255) NULL COMMENT '会话ID',
    knowledgeDirId VARCHAR(255) NULL COMMENT '知识目录ID',
    knowledgeTag  VARCHAR(255) NULL COMMENT '知识聚类标签',
    createTime     DATETIME     NULL COMMENT '创建时间',
    updateTime     DATETIME     NULL COMMENT '更新时间',
    content        VARCHAR(255) NULL COMMENT '内容'
    )
    ENGINE=OLAP  COMMENT 'sop 知识引用表'
    DISTRIBUTED BY HASH(taskId, topicId, nodeId) BUCKETS 10
    PROPERTIES (
                   "replication_allocation" = "tag.location.default: 3",
                   "storage_format"         = "V2",
                   "light_schema_change"    = "true",
                   "disable_auto_compaction"= "false"
               );