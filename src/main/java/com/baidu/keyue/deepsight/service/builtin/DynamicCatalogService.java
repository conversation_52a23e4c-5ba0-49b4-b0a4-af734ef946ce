package com.baidu.keyue.deepsight.service.builtin;

import com.baidu.keyue.deepsight.constants.BuiltinLabelConstants;
import com.baidu.keyue.deepsight.constants.SqlConstants;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.utils.SqlUtils;
import com.baidu.keyue.deepsight.utils.WebContextUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.BuiltinLabelCatalogEnum;
import com.baidu.keyue.deepsight.models.catalog.NewCatalogRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

/**
 * 动态目录管理服务
 * 负责处理基于数据动态生成的目录结构
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Service
public class DynamicCatalogService {

    @Autowired
    private DorisService dorisService;
    
    @Autowired
    private LabelCatalogService labelCatalogService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取指定租户的机器人名称列表
     * @param tenantId 租户ID
     * @return 机器人名称列表，如果没有数据返回空列表
     * <AUTHOR>
     */
    public List<String> getRobotNames(String tenantId) {
        try {
            String tableName = String.format(BuiltinLabelConstants.DynamicCatalog.SESSION_TABLE_TEMPLATE, tenantId);
            String sql = SqlUtils.buildQueryRobotNamesSQL(tableName);

            List<Map<String, Object>> result = dorisService.selectList(sql);

            if (CollectionUtils.isEmpty(result)) {
                log.info("租户 {} 的表 {} 中没有找到机器人名称数据", tenantId, tableName);
                return Collections.emptyList();
            }

            List<String> robotNames = result.stream()
                    .map(row -> (String) row.get(SqlConstants.FieldNames.ROBOT_NAME))
                    .filter(name -> name != null && !name.trim().isEmpty())
                    .distinct()
                    .collect(Collectors.toList());

            log.info("租户 {} 找到 {} 个机器人: {}", tenantId, robotNames.size(), robotNames);
            return robotNames;

        } catch (Exception e) {
            log.error("获取租户 {} 机器人名称失败", tenantId, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 获取指定租户的tagExtractInfo字段中robotName与key值的映射关系
     * @param tenantId 租户ID
     * @return robotName到key值列表的映射，如果没有数据返回空Map
     * <AUTHOR>
     */
    public Map<String, List<String>> getTagExtractKeysWithRobotName(String tenantId) {
        try {
            String tableName = String.format(BuiltinLabelConstants.DynamicCatalog.SESSION_TABLE_TEMPLATE, tenantId);
            String sql = SqlUtils.buildQueryTagExtractWithRobotNameSQL(tableName);

            List<Map<String, Object>> result = dorisService.selectList(sql);

            if (CollectionUtils.isEmpty(result)) {
                log.info("租户 {} 的表 {} 中没有找到tagExtractInfo和robotName数据", tenantId, tableName);
                return Collections.emptyMap();
            }

            Map<String, Set<String>> robotNameToKeysMap = new HashMap<>();

            for (Map<String, Object> row : result) {
                String robotName = (String) row.get(SqlConstants.FieldNames.ROBOT_NAME);
                String tagExtractInfo = (String) row.get(SqlConstants.FieldNames.TAG_EXTRACT_INFO);

                if (robotName != null && !robotName.trim().isEmpty() &&
                    tagExtractInfo != null && !tagExtractInfo.trim().isEmpty()) {
                    try {
                        // 解析JSON数组
                        List<Map<String, Object>> tagList = objectMapper.readValue(
                            tagExtractInfo,
                                new TypeReference<>() {
                                }
                        );

                        // 提取该robotName对应的key值
                        Set<String> keys = robotNameToKeysMap.computeIfAbsent(robotName.trim(), k -> new HashSet<>());
                        for (Map<String, Object> tag : tagList) {
                            String key = (String) tag.get(SqlConstants.FieldNames.JSON_KEY);
                            if (key != null && !key.trim().isEmpty()) {
                                keys.add(key.trim());
                            }
                        }
                    } catch (Exception e) {
                        log.warn("解析robotName {} 的tagExtractInfo JSON失败: {}", robotName, tagExtractInfo, e);
                    }
                }
            }

            // 转换为List形式并排序
            Map<String, List<String>> resultMap = robotNameToKeysMap.entrySet().stream()
                    .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream().sorted().collect(Collectors.toList())
                    ));

            log.info("租户 {} 找到 {} 个robotName的tagExtractInfo映射: {}", tenantId, resultMap.size(), resultMap);
            return resultMap;

        } catch (Exception e) {
            log.error("获取租户 {} tagExtractInfo与robotName映射失败", tenantId, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 为对话挖掘二级目录创建机器人名称三级目录
     * 动态获取robotName，如果取不到则不创建
     * 
     * @param tenantId 租户ID
     */
    public void createRobotNameCatalogs(String tenantId) {
        DeepSightWebContext originalContext = WebContextHolder.getDeepSightWebContext();
        try {
            // 设置系统用户上下文
            DeepSightWebContext systemContext = WebContextUtils.createOrUpdateSystemContext(tenantId);
            WebContextHolder.setDeepSightWebContext(systemContext);

            // 1. 确保对话挖掘二级目录存在
            LabelCatalog conversationMiningCatalog;
            try {
                conversationMiningCatalog = labelCatalogService.checkLabelCatalog(tenantId, 
                    BuiltinLabelCatalogEnum.CONVERSATION_MINING.getCatalogName());
            } catch (Exception e) {
                log.warn("租户 {} 对话挖掘目录不存在，先创建该目录", tenantId);
                // 创建对话挖掘目录
                NewCatalogRequest catalogRequest = new NewCatalogRequest();
                catalogRequest.setCatalogName(BuiltinLabelCatalogEnum.CONVERSATION_MINING.getCatalogName());
                catalogRequest.setParentId(BuiltinLabelConstants.CatalogHierarchy.ROOT_CATALOG_ID);
                labelCatalogService.createLabelCatalog(catalogRequest);
                
                // 重新获取创建的目录
                conversationMiningCatalog = labelCatalogService.checkLabelCatalog(tenantId,
                    BuiltinLabelCatalogEnum.CONVERSATION_MINING.getCatalogName());
            }
            
            // 2. 获取机器人名称列表
            List<String> robotNames = getRobotNames(tenantId);
            
            if (CollectionUtils.isEmpty(robotNames)) {
                log.info("租户 {} 没有找到机器人名称，跳过机器人名称目录创建", tenantId);
                return;
            }
            
            // 3. 为每个机器人名称创建三级目录
            for (String robotName : robotNames) {
                try {
                    // 检查目录是否已存在
                    labelCatalogService.checkLabelCatalog(tenantId, robotName);
                    log.debug("租户 {} 机器人目录 {} 已存在，跳过创建", tenantId, robotName);
                    
                } catch (Exception e) {
                    // 目录不存在，创建机器人名称三级目录
                    log.info("租户 {} 创建机器人目录: {}", tenantId, robotName);
                    try {
                        NewCatalogRequest request = new NewCatalogRequest();
                        request.setCatalogName(robotName);
                        request.setParentId(conversationMiningCatalog.getId());
                        
                        labelCatalogService.createLabelCatalog(request);
                        log.info("租户 {} 成功创建机器人目录: {}", tenantId, robotName);
                        
                    } catch (Exception createException) {
                        log.error("租户 {} 创建机器人目录 {} 失败", tenantId, robotName, createException);
                    }
                }
            }
            
            log.info("租户 {} 机器人名称目录创建完成", tenantId);
            
        } catch (Exception e) {
            log.error("租户 {} 创建机器人名称目录失败", tenantId, e);
        } finally {
            // 恢复原始上下文
            if (originalContext != null) {
                WebContextHolder.setDeepSightWebContext(originalContext);
            }
        }
    }

 }