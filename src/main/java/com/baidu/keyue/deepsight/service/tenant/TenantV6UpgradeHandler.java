package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.database.doris.DorisTableTemplateConfig;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelService;
import com.baidu.keyue.deepsight.service.builtin.UserMetricTableInitService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName TenantV3UpgradeHandler
 * @Description v1.1.3租户升级
 * <AUTHOR>
 * @Date 2025/6/20 3:18 PM
 */
@Slf4j
@Service
@Order(7)
public class TenantV6UpgradeHandler extends TenantUpgradeHandler {

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private TenantInfoMapper tenantInfoMapper;

    @Resource
    @Lazy
    private BuiltinLabelService builtinLabelService;

    @Resource
    private DorisTableTemplateConfig dorisTableTemplateConfig;

    @Resource
    private TableRecordCommonService tableRecordCommonService;

    @Resource
    private DorisService dorisService;

    @Resource
    @Lazy
    private TenantInfoService tenantInfoService;

    @Resource
    private UserMetricTableInitService userMetricTableInitService;

    /**
     * 外呼对话内容新增字段
     */
    private final List<String> aiobRecordField = List.of("nodeInfo");

    private final Map<String, TableFieldMetaInfo> aiobRecordFieldMap = new HashMap<>();

    @PostConstruct
    public void init() throws IOException {
        setVersion(7);
        initDorisSql("upgrade/doris/v1.2.2.sql");
        dorisTableTemplateConfig.getAiobRecordFieldsInfo().forEach(field -> aiobRecordFieldMap.put(field.getEnField(), field));

        // 全量租户 user_metric 表元数据初始化
        initUserMetricMetadataForAllTenants();
    }

    public TenantV6UpgradeHandler(DorisService dorisService) {
        super(dorisService);
    }

    @Override
    protected boolean needUpdateTenant(TenantInfo tenant) {
        TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(tenant.getTenantid());
        Integer version = tenantInfo.getVersion();
        tenant.setUserId(tenantInfo.getUserId());
        return Objects.equals(version, 6);
    }

    @Override
    protected void executeUpgrade(TenantDTO tenant) {
        transactionTemplate.execute(status -> {
            try {
                String tenantId = tenant.getTenantId();
                log.info("tenant v1.2.2 upgrade start.tenantId is {}.", tenantId);

                // 新旧租户-user_metric表元数据初始化：
                ensureUserMetricMetadata(tenantId);
                log.info("v1.2.2 Upgrade close ensureUserMetricMetadata over");

                // 新旧租户-内置标签：
                createBuiltinLabels(tenantId);
                log.info("v1.2.2 Upgrade close createBuiltinLabels over");

                // 老租户升级历史数据
                if (!tenant.getIsInit()) {
                    // 历史表操作
                    oldTableUpgrade(tenantId);
                    log.info("v1.1.2 Upgrade oldTableUpgrade over");
                }

                // 更新责任链租户信息
                TenantInfo info = tenant.getTenantInfo();
                info.setVersion(version);
                // 更新数据库租户版本
                TenantInfo tenantInfo = new TenantInfo();
                tenantInfo.setVersion(version);
                tenantInfo.setId(info.getId());
                tenantInfo.setUpdateTime(new Date());
                tenantInfoMapper.updateByPrimaryKeySelective(tenantInfo);
            } catch (Exception e) {
                log.error("tenant v1.2.2 Upgrade error!, tenantId:{} ", tenant.getTenantId(), e);
                status.setRollbackOnly();
                throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR, "租户v1.2.2版本升级失败");
            }
            return null;
        });
    }

    /**
     * 全量租户 user_metric 表元数据初始化
     * 在系统启动时执行，为所有历史租户初始化 user_metric 表元数据
     */
    private void initUserMetricMetadataForAllTenants() {
        try {
            log.info("开始全量租户 user_metric 表元数据初始化");
            List<String> allTenantIds = tenantInfoService.getAllTenantInfo().stream().map(TenantInfo::getTenantid).toList();
            log.info("获取到 {} 个租户，开始批量初始化 user_metric 表元数据", allTenantIds.size());

            int successCount = 0;
            int skipCount = 0;
            int failCount = 0;

            for (String tenantId : allTenantIds) {
                try {
                    // 检查是否已存在（检查三个关键字段）
                    if (isUserMetricMetadataExists(tenantId)) {
                        log.debug("租户 {} 的 user_metric 表元数据已存在，跳过", tenantId);
                        skipCount++;
                        continue;
                    }

                    // 初始化
                    userMetricTableInitService.initUserMetricTableMetadata(tenantId);
                    log.info("租户 {} 的 user_metric 表元数据初始化成功", tenantId);
                    successCount++;

                } catch (Exception e) {
                    log.error("租户 {} 的 user_metric 表元数据初始化失败", tenantId, e);
                    failCount++;
                }
            }

            log.info("全量租户 user_metric 表元数据初始化完成，总数: {}, 成功: {}, 跳过: {}, 失败: {}",
                    allTenantIds.size(), successCount, skipCount, failCount);

        } catch (Exception e) {
            log.error("全量租户 user_metric 表元数据初始化失败", e);
            // 不抛出异常，避免影响系统启动
        }
    }

    /**
     * 确保单个租户的 user_metric 表元数据存在
     * 在租户升级时执行，先判断有没有，没有就升级
     */
    private void ensureUserMetricMetadata(String tenantId) {
        try {
            // 检查是否已存在（检查三个关键字段）
            if (isUserMetricMetadataExists(tenantId)) {
                log.debug("租户 {} 的 user_metric 表元数据已存在，跳过初始化", tenantId);
                return;
            }

            // 初始化
            userMetricTableInitService.initUserMetricTableMetadata(tenantId);
            log.info("租户 {} 的 user_metric 表元数据初始化成功", tenantId);

        } catch (Exception e) {
            log.error("租户 {} 的 user_metric 表元数据初始化失败", tenantId, e);
            throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR,
                    "user_metric表元数据初始化失败: " + e.getMessage());
        }
    }

    /**
     * 检查 user_metric 表元数据是否存在
     * 检查三个关键字段：avg_rounds, connect_rate, first_round_hangup_rate
     */
    private boolean isUserMetricMetadataExists(String tenantId) {
        try {
            // 检查三个关键字段是否都存在
            userMetricTableInitService.getUserMetricFieldId(tenantId, "avg_rounds");
            userMetricTableInitService.getUserMetricFieldId(tenantId, "connect_rate");
            userMetricTableInitService.getUserMetricFieldId(tenantId, "first_round_hangup_rate");
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 创建内置标签
     *
     * @param tenantId 租户ID
     */
    public void createBuiltinLabels(String tenantId) {
        try {
            log.info("开始为租户 {} 创建内置标签", tenantId);
            builtinLabelService.createBuiltinLabelsForTenant(tenantId);
            log.info("租户 {} 内置标签创建完成", tenantId);
        } catch (Exception e) {
            log.error("租户 {} 内置标签创建失败", tenantId, e);
            // 不抛出异常，避免影响租户升级流程
        }
    }

    /**
     * 老租户表升级
     * 只针对老租户的升级
     * Doris、mysql字段信息修改、添加、删除
     *
     * @param tenantId 租户id
     */
    public void oldTableUpgrade(String tenantId) {
        // 执行除建表外的其他语句：历史Doris表新增字段
        for (String sql : this.sqlList) {
            if (sql.toUpperCase().contains("CREATE TABLE")) {
                continue;
            }
            // 将^&符号作为租户ID替代符号
            String execSql = sql.replaceAll("\\^&", tenantId) + ";";
            dorisService.operationSchema(execSql);
        }
        // 外呼对话内容表增加字段元信息
        String aiobRecordTableName = TenantUtils.generateAiobRecordTableName(tenantId);
        tableRecordCommonService.saveUpgradeFieldMetaInfo(aiobRecordTableName, aiobRecordField, aiobRecordFieldMap);
    }
}
