package com.baidu.keyue.deepsight.service.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.AiobFailReasonEnum;
import com.baidu.keyue.deepsight.enums.AiobLineStatusEnum;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableRecordMsgDTO;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.ai.AssistantRecognitionService;
import com.baidu.keyue.deepsight.service.datamanage.AiobRobotVersionService;
import com.baidu.keyue.deepsight.service.datamanage.TableContentService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.service.tenant.impl.TenantInfoServiceImpl;
import com.baidu.keyue.deepsight.utils.AESUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.QueryBuilders;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName AIOBConversationDataWorker
 * @Description 外呼对话内容、记录数据消费
 * <AUTHOR>
 * @Date 2025/2/27 2:30 PM
 */
@Slf4j
@Component
public class AIOBConversationDataWorker extends AbstractDataSyncWorker {

    @Resource
    private RestHighLevelClient client;

    @Resource
    private TableRecordCommonService commonService;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Resource
    private TenantInfoService tenantInfoService;

    @Resource
    private AiobRobotVersionService aiobRobotVersionService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private AiobSOPService aiobSOPService;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private AssistantRecognitionService assistantRecognitionService;

    @Value("${kafka.topics.dataSync:deep_sight_data_sync}")
    private String topic;

    @Autowired
    private RedissonClient redisson;
    
    @Value("${aiob.default-task-ids:1,2}")
    private String defaultTaskIdStr;

    /**
     * TODO 临时开关
     */
    @Value("${temp.kafka.switch:false}")
    private boolean kafkaFaqSwitch;

    private static final String AIOB_RECORD_INDEX = "aiob-conversation-record-*";

    private final Set<String> codesSet = new HashSet<>(Arrays.asList("record", "session", "user"));
    private final Set<String> defaultTaskIds = new HashSet<>();

    public AIOBConversationDataWorker(TableContentService contentService,
                                      TenantInfoServiceImpl tenantInfoService) {
        super(contentService, tenantInfoService);
    }
    
    @PostConstruct
    public void init() {
        if (StrUtil.isNotBlank(defaultTaskIdStr)) {
            defaultTaskIds.addAll(Arrays.asList(defaultTaskIdStr.split(",")));
        }
    }

    @Override
    protected void processData(String data) {
        String dataType = "aiob-record";
        try {
            Map<String, Object> msgMap = JsonUtils.toMap(data);
            // 只处理包含Code字段的数据，Code目前处理类型：record、session
            String code = String.valueOf(msgMap.get("code"));
            if (!codesSet.contains(code)) {
                return;
            }
            Map<String, Object> map = JsonUtils.toMap(JsonUtils.toJson(msgMap.get("data")));
            // 公共参数校验
            String tenantId = String.valueOf(map.get("tenantId"));
            if (StrUtil.isBlank(tenantId)) {
                return;
            }
            // 租户不存在，直接跳过
            TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(tenantId);
            if (null == tenantInfo) {
                return;
            }
            String tableName = null;
            // 外呼回话处理
            if (Objects.equals("record", code)) {
                tableName = TenantUtils.generateAiobRecordTableName(tenantId);
                if (!checkAiobRecord(map) || !isDuplicateData(map, code, tenantId)) {
                    return;
                }
                // 必要的值映射、补充
                convertAiobRecord(map);
            } else if (Objects.equals("session", code)) {
                tableName = TenantUtils.generateAiobSessionTableName(tenantId);
                dataType = "aiob-session";
                if (!checkAiobSession(map) || !isDuplicateData(map, code, tenantId)) {
                    log.debug("aioBConversationDataWorker processData checkAiobSession failed, data: {}, code: {}, tenantId: {}", data, code, tenantId);
                    return;
                }
                // 必要的值映射、补充
                convertAiobSession(map);
                // 添加机器人版本信息
                addRobotVersion(map);
            } else if (Objects.equals("user", code)) {
                tableName = TenantUtils.generateMockUserTableName(tenantId);
                dataType = "aiob-user";
                checkAiobUser(map);
                // user数据解密后重复校验
                if (!isDuplicateData(map, code, tenantId)) {
                    return;
                }
            }
            // 校验映射后的字段
            DataTableInfo dataTableInfo = commonService.getDataTableDetail(tableName, tenantId);
            commonService.recordCheck(map,
                    commonService.getRequiredFields(dataTableInfo.getId()),
                    commonService.getFieldsType(dataTableInfo.getId()), DbTypeEnum.DORIS_TYPE.getDbType());
            // 下发校验通过的结构化的session_service数据。
            TableRecordMsgDTO recordMsg = new TableRecordMsgDTO();
            recordMsg.setCode(tableName);
            recordMsg.setData(map);
            String msg = objectMapper.writeValueAsString(recordMsg);
            log.debug("send waihu data sync mq, msg is {}", msg);
            kafkaTemplate.send(topic, msg);
        } catch (Exception e) {
            log.error("waihu {} process error. data is {}", dataType, data, e);
        }
    }

    /**
     * 获取机器人版本信息
     */
    public void addRobotVersion(Map<String, Object> map) {
        // 检查 map 中是否存在 robotScene 键且其值为 5
        if (ObjectUtils.isNotEmpty(map.get("robotScene")) && (Integer) (map.get("robotScene")) == 5
            && ObjectUtils.isNotEmpty(map.get("botVersionId")) && Objects.equals((String) (map.get("sipCode")), "200")) {
            // 获取 botVersionId
            String botVersionId = (String) map.get("botVersionId");
            // 获取 robotId
            String robotId = (String) map.get("robotId");
            // 获取 botVersionName
            String botVersionName = (String) map.get("botVersionName");
            // 获取 tenantId
            String tenantId = map.get("tenantId").toString();

            Long publishTime = (Long) map.get("publishTime");
            // 检查 aiobRobotVersionService 中是否存在该版本的机器人
            if (aiobRobotVersionService.getAiobRobotVersion(tenantId, robotId, botVersionId)) {
                // 如果存在，则直接返回
                return;
            }
            // 如果不存在，则保存该版本的机器人信息
            aiobRobotVersionService.saveAiobRobotVersion(tenantId, robotId, botVersionId, botVersionName, publishTime);
        }

    }

    /**
     * 外呼记录值转换
     * 1、didOwner转字符串
     * 2、手机号解密&加密
     * 3、补充recordCount
     *
     * @param map 外呼记录
     */
    public void convertAiobSession(Map<String, Object> map) {
        if (null != map.get("botVersionId")) {
            map.put("botVersionId", String.valueOf(map.get("botVersionId")));
        }
        String tenantId = (String) map.get("tenantId");
        String sessionId = (String) map.get("sessionId");
        String taskId = (String) map.get("taskId");
        if (ObjectUtils.isNotEmpty(map.get("didOwner"))) {
            map.put("didOwner", map.get("didOwner").toString());
        }
        DataTableInfo dataTableInfo = commonService.getDataTableDetail(tenantId);
        // 解密手机号&加密
        handleAiobMoileField(map, dataTableInfo.getId());
        // 获取recordCount
        Long count = getRecordCntBySessionId(sessionId);
        map.put("recordCount", count);

        // 填充customTagList字段
        if (null != map.get("customTagList")) {
            List<Map<String, Object>> tagList = (List<Map<String, Object>>) map.get("customTagList");
            List<String> tagListStr = new ArrayList<>();
            for (Map<String, Object> tag : tagList) {
                tagListStr.add(tag.get("tag").toString());
            }
            map.put("customTagList", tagListStr);
        }
        // 映射未接通原因,endType=0表示未接通
        Object endTypeObj = map.get("endType");
        if (Objects.equals(String.valueOf(endTypeObj), "0")) {
            Object endTypeReason = map.get("endTypeReason");
            Object sipCode = map.get("sipCode");
            String hangupReason = String.valueOf(map.get("hangupReason"));
            AiobFailReasonEnum reasonEnum = AiobFailReasonEnum.create(String.valueOf(endTypeReason), String.valueOf(sipCode), hangupReason);
            Optional.ofNullable(reasonEnum).ifPresent(reason -> {
                map.put("dicCategory", reason.getTypeCategory().getName());
                map.put("dicName", reason.getTypeName());
            });
        }
        map.put("lineStatus", AiobLineStatusEnum.ENABLED.getValue());


        // 小秘书识别逻辑
        performAssistantRecognition(map);

        // 创建 SopUserConfig 该函数添加了较长时间的缓存 避免重复调用
        // aiobSOPService.getOrCreateSopUserConfig(tenantId, taskId, Constants.SYSTEM_DEFAULT_USER_ID);
    }

    /**
     * 外呼内容值转换
     * 1、映射role为roleType
     *
     * @param map 外呼内容
     */
    public void convertAiobRecord(Map<String, Object> map) {
        Object role = map.get("role");
        Optional.ofNullable(role).ifPresent(ro -> map.put("roleType", ro));

        // 解码并存储nodeInfo
        decodeAndStoreNodeInfo(map);

        // record获取queryId
        getQueryIdByNodeInfo(map);
    }

    /**
     * 检查外呼记录数据
     *
     * @param map 外呼用户数据
     * @return 是否检查通过
     */
    public boolean checkAiobUser(Map<String, Object> map) {
        // 解密手机号
        String mobile = (String) map.get("mobile");
        if (StrUtil.isBlank(mobile)) {
            return false;
        }
        if ((ObjectUtils.isNotEmpty(map.get("secretType")) && 3 == (Integer) map.get("secretType"))) {
            return false;
        }
        String tenantId = map.get("tenantId").toString();
        DataTableInfo dataTableInfo = commonService.getDataTableDetail(tenantId);
        handleAiobMoileField(map, dataTableInfo.getId());
        map.put("user_id", UUID.randomUUID().toString());
        return true;
    }

    /**
     * 解码nodeInfo并存储为JSON格式
     * @param map
     */
    private void decodeAndStoreNodeInfo(Map<String, Object> map) {
        Object nodeInfoObj = map.get("nodeInfo");
        if (nodeInfoObj == null || StringUtils.isEmpty(nodeInfoObj.toString())) {
            return;
        }

        try {
            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(nodeInfoObj.toString());
            String decodedJson = new String(decodedBytes);

            // 验证JSON格式
            Map<String, Object> nodeInfoMap = objectMapper.readValue(decodedJson, Map.class);

            // 存储解码后的JSON字符串
            map.put("nodeInfo", decodedJson);

            log.debug("nodeInfo解码成功, sessionId: {}, nodeInfo: {}", map.get("sessionId"), decodedJson);

        } catch (Exception e) {
            log.error("nodeInfo解码失败, sessionId: {}, nodeInfo: {}", map.get("sessionId"), nodeInfoObj, e);
            // 解码失败时设置为null，避免存储无效数据
            map.put("nodeInfo", null);
        }
    }

    /**
     * 获取record 中queryId
     *
     * @param map 外呼记录
     * @return 是否检查通过
     */
    public void getQueryIdByNodeInfo(Map<String, Object> map) {
        Object nodeInfoObj = map.get("nodeInfo");
        if (nodeInfoObj == null || StringUtils.isEmpty(nodeInfoObj.toString())) {
            return;
        }

        try {
            // 直接解析JSON字符串
            Map<String, Object> nodeInfoMap = objectMapper.readValue(nodeInfoObj.toString(), Map.class);
            if (nodeInfoMap.containsKey("queryId")) {
                map.put("queryId", nodeInfoMap.get("queryId"));
            }
            if (kafkaFaqSwitch){
                // 快捷场景增加faq
                if (map.containsKey("faq")) {
                    map.put("faq", map.get("faq"));
                }
            }


        } catch (Exception e) {
            log.error("从nodeInfo提取queryId失败, sessionId: {}", map.get("sessionId"), e);
        }
    }

    /**
     * 检查外记录数据
     *
     * @param map 外呼记录
     * @return 是否检查通过
     */
    public boolean checkAiobSession(Map<String, Object> map) {
        // 主键检查
        String sessionId = (String) map.get("sessionId");
        if (StrUtil.isBlank(sessionId)
                || "4".equals(map.get("taskType"))
                || (ObjectUtils.isNotEmpty(map.get("secretType"))
                && 3 == (Integer) map.get("secretType"))) {
            return false;
        }
        // 检查跳过内置任务
        if (defaultTaskIds.contains(String.valueOf(map.get("taskId")))) {
            return false;
        }
        return true;
    }


    /**
     * 检查外呼对话内容数据
     *
     * @param map 外呼内容
     * @return 是否检查通过
     */
    public boolean checkAiobRecord(Map<String, Object> map) {
        // 主键检查
        String evtSequenceNumber = (String) map.get("EvtSequenceNumber");
        // ...其他检查
        return StrUtil.isNotBlank(evtSequenceNumber);
    }


    /**
     * 查询外呼record_count数
     */
    public Long getRecordCntBySessionId(String sessionId) {
        CountRequest countRequest = new CountRequest(AIOB_RECORD_INDEX);
        countRequest.query(QueryBuilders.termQuery("sessionId", sessionId));
        Long cnt = 0L;
        try {
            CountResponse countResponse = client.count(countRequest, RequestOptions.DEFAULT);
            cnt = countResponse.getCount();
        } catch (Exception e) {
            log.error("query aibo record count error", e);
        }
        return cnt;

    }

    /**
     * 判断数据是否重复
     */
    public Boolean isDuplicateData(Map<String, Object> map, String type, String tenantId) {
        String id = null;
        String sql = null;
        if (Objects.equals("record", type)) {
            id = (String) map.get("EvtSequenceNumber");
            sql = ORMUtils.generateCheckDuplicateDataSql(TenantUtils.generateAiobRecordTableName(tenantId), id, "EvtSequenceNumber");
        } else if (Objects.equals("session", type)) {
            id = (String) map.get("sessionId");
            sql = ORMUtils.generateCheckDuplicateDataSql(TenantUtils.generateAiobSessionTableName(tenantId), id, "sessionId");
        } else if (Objects.equals("user", type)) {
            id = (String) map.get("mobile");
            String key = tenantId + "_mobile_" + id;
            RBucket<String> bucket = redisson.getBucket(key);
            boolean success = bucket.trySet("1", 10, TimeUnit.MINUTES); // 如果 key 不存在则 set 并返回 true
            if (!success) {
                return false;
            }
            sql = ORMUtils.generateCheckDuplicateDataSql(TenantUtils.generateMockUserTableName(tenantId), id, "mobile");
        } else {
            return false;
        }
        return dorisService.getCount(sql) == 0L ? true : false;
    }

    /**
     * 外呼数据解密手机号再加密
     */
    public Boolean handleAiobMoileField(Map<String, Object> map, Long dataTableId) {
        String mobile = map.get("mobile").toString();
        // 获取密钥
        String key = commonService.getAiobSecretKey((Integer) map.get("secretType"),
                Long.parseLong(map.get("tenantId").toString()), (String) map.get("secretKey"));
        if (null == key && ObjectUtils.isNotEmpty(map.get("secretType"))
                && (5 == (Integer) map.get("secretType") || 3 == (Integer) map.get("secretType"))) {
            log.warn("aiob query secretKey is null.item is {}", JSON.toJSONString(map));
            throw new DeepSightException.ParamsErrorException(
                    ErrorCode.NOT_FOUND, "handle aiob mobile encrypt error");
        }
        // secretType不为空才需要解密
        if (ObjectUtils.isNotEmpty(map.get("secretType")) && null != key) {
            if (2 == (Integer) map.get("secretType") || 4 == (Integer) map.get("secretType")) {
                mobile = AESUtils.decrypt(map.get("mobile").toString(), key);
            } else if (1 == (Integer) map.get("secretType")) {
                mobile = AESUtils.decryptAES(map.get("mobile").toString(), key);
            }
        }
        map.put("mobile", mobile);
        // 计算md5
        String md5 = DigestUtils.md5Hex(mobile);
        map.put("mobileMD5", md5);
        // 加密
        commonService.encryptItem(map, commonService.getEncryptFields(dataTableId));
        return true;
    }


    /**
     * 执行小秘书识别
     */
    private void performAssistantRecognition(Map<String, Object> map) {
        try {
            String tenantId = (String) map.get("tenantId");
            String sessionId = (String) map.get("sessionId");
            String taskId = (String) map.get("taskId");

            // 检查前置条件1和3
            if (!checkBasicConditions(map)) {
                map.put("is_auto_answer", 0); // 不满足基本条件
                return;
            }

            // 获取对话内容
            String conversationContent = (String) map.get("conversationContent");
            if (StrUtil.isBlank(conversationContent)) {
                map.put("is_auto_answer", 0);
                return;
            }

            // 调用识别服务
            Boolean isAssistant = assistantRecognitionService.recognizeAssistant(
                sessionId, taskId, conversationContent, tenantId);

            log.info("assistant recognition result is {}", isAssistant);
            if (isAssistant != null) {
                map.put("is_auto_answer", isAssistant ? 1 : 0);
            } else {
                map.put("is_auto_answer", 0); // 识别失败默认为非小秘书
            }

        } catch (Exception e) {
            log.error("小秘书识别处理失败, sessionId: {}", map.get("sessionId"), e);
            map.put("is_auto_answer", 0); // 异常情况默认为非小秘书
        }
    }

    /**
     * 检查基本条件
     * 条件1: 接通且外呼机器人主动挂断
     * 条件3: 对话轮次 ≥ 2
     */
    private boolean checkBasicConditions(Map<String, Object> map) {
        // 条件1：接通且外呼机器人主动挂断
        String sipCode = (String) map.get("sipCode");
        Boolean isRobotHangup = (Boolean) map.get("isRobotHangup");

        if (!"200".equals(sipCode) || !Boolean.TRUE.equals(isRobotHangup)) {
            return false;
        }

        // 条件3：对话轮次≥2
        Object talkingTurnObj = map.get("talkingTurn");
        if (talkingTurnObj == null) {
            return false;
        }

        int talkingTurn;
        if (talkingTurnObj instanceof Number) {
            talkingTurn = ((Number) talkingTurnObj).intValue();
        } else {
            try {
                talkingTurn = Integer.parseInt(talkingTurnObj.toString());
            } catch (NumberFormatException e) {
                return false;
            }
        }

        return talkingTurn >= 2;
    }

    /**
     * 从 kafka 监听器消费消息，处理历史记录日志数据
     *
     * @param msg 消息
     */
    @KafkaListener(topics = "${deepSight.kafka.topic.aiob:deep_sight_aiob_data_sync}")
    public void conversationHistoryLogConsumer(String msg) {
        log.debug("外呼receive msg is {}", msg);
        processData(msg);
    }
}
