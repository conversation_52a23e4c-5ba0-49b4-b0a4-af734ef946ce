package com.baidu.keyue.deepsight.service.sop;

import lombok.Getter;

/**
 *  Sop 任务意向标签
 */
@Getter
public enum SopTaskTagEnum {


    A("A级（有明确意向）"),
    B("B级（可能有意向）"),
    C("C级（无意向）"),
    D("D级（用户在忙）"),
    ;


    private final String tagName;

    SopTaskTagEnum(String tagName) {
        this.tagName = tagName;
    }

    public static boolean intentionTag(String tag){
        return A.getTagName().equals(tag) || B.getTagName().equals(tag);
    }
}
