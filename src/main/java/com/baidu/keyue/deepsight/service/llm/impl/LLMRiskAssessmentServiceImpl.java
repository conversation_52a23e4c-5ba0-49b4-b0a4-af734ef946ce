package com.baidu.keyue.deepsight.service.llm.impl;

import cn.hutool.core.util.StrUtil;
import com.baidu.keyue.deepsight.config.LLMRiskAssessmentConfig;
import com.baidu.keyue.deepsight.constants.LLMServiceConstants;
import com.baidu.keyue.deepsight.constants.RiskAssessmentConstants;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.llm.dto.LLMRiskAssessmentRequest;
import com.baidu.keyue.deepsight.models.llm.dto.LLMRiskAssessmentResponse;
import com.baidu.keyue.deepsight.models.llm.dto.LLMRiskResult;
import com.baidu.keyue.deepsight.service.llm.LLMRiskAssessmentService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.kybase.commons.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * LLM风险评估服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@Service
public class LLMRiskAssessmentServiceImpl implements LLMRiskAssessmentService {

    @Autowired
    private LLMRiskAssessmentConfig config;

    @Override
    @Cacheable(value = "llmRiskCache", key = "T(com.baidu.keyue.deepsight.service.llm.impl.LLMRiskAssessmentServiceImpl)" +
            ".generateCacheKey(#conversationContent)",
               cacheManager = "llmRiskCacheManager")
    public String assessRisk(String conversationContent) {
        // 参数校验
        if (StrUtil.isBlank(conversationContent)) {
            log.warn("对话内容为空，返回默认风险等级");
            return LLMServiceConstants.DefaultValues.DEFAULT_RISK_LEVEL;
        }

        try {
            // 构建请求
            LLMRiskAssessmentRequest request = LLMRiskAssessmentRequest.build(
                config.getEnvironment(), 
                conversationContent
            );

            // 构建请求头
            Map<String, String> headers = buildHeaders();

            // 发送HTTP请求
            String requestJson = JsonUtils.toJsonWithOutException(request);
            log.debug("调用LLM风险评估服务，URL: {}, 请求体: {}", config.getUrl(), requestJson);

            String responseJson = HttpUtil.postJsonWithTry(config.getUrl(), requestJson, headers);
            log.debug("LLM风险评估服务响应: {}", responseJson);

            // 解析响应
            return parseResponse(responseJson);

        } catch (Exception e) {
            log.error("调用LLM风险评估服务异常，对话内容长度: {}", conversationContent.length(), e);
            throw new DeepSightException.ParamsErrorException(
                ErrorCode.INTERNAL_ERROR,
                LLMServiceConstants.ErrorMessages.LLM_SERVICE_CALL_FAILED
            );
        }
    }

    /**
     * 构建请求头
     * 
     * @return 请求头Map
     */
    private Map<String, String> buildHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put(LLMServiceConstants.HttpConstants.CONTENT_TYPE_HEADER,
                   LLMServiceConstants.HttpConstants.APPLICATION_JSON);
        headers.put(LLMServiceConstants.HttpConstants.TOKEN_HEADER, config.getToken());
        return headers;
    }

    /**
     * 解析LLM服务响应
     * 
     * @param responseJson 响应JSON字符串
     * @return 风险等级
     */
    private String parseResponse(String responseJson) {
        try {
            // 解析外层响应
            LLMRiskAssessmentResponse response = JsonUtils.toObjectWithoutException(
                responseJson, LLMRiskAssessmentResponse.class
            );

            if (response == null || !response.isSuccess()) {
                log.error("LLM服务返回状态码错误: {}", responseJson);
                throw new DeepSightException.ParamsErrorException(
                    ErrorCode.INTERNAL_ERROR,
                    LLMServiceConstants.ErrorMessages.LLM_RESPONSE_CODE_ERROR
                );
            }

            // 解析output字段中的JSON
            String output = response.getData().getOutput();
            if (StrUtil.isBlank(output)) {
                log.error("LLM服务返回output为空: {}", responseJson);
                throw new DeepSightException.ParamsErrorException(
                    ErrorCode.INTERNAL_ERROR,
                    LLMServiceConstants.ErrorMessages.LLM_RESPONSE_FORMAT_ERROR
                );
            }

            // 提取JSON内容（去除```json和```标记）
            String cleanJson = extractJsonFromOutput(output);
            
            // 解析风险结果
            LLMRiskResult riskResult = JsonUtils.toObjectWithoutException(
                cleanJson, LLMRiskResult.class
            );

            if (riskResult == null || StrUtil.isBlank(riskResult.getResult())) {
                log.error("解析风险结果失败: {}", cleanJson);
                throw new DeepSightException.ParamsErrorException(
                    ErrorCode.INTERNAL_ERROR,
                    LLMServiceConstants.ErrorMessages.LLM_RESPONSE_FORMAT_ERROR
                );
            }

            // 映射风险等级
            return mapRiskLevel(riskResult.getResult());

        } catch (Exception e) {
            log.error("解析LLM服务响应失败: {}", responseJson, e);
            throw new DeepSightException.ParamsErrorException(
                ErrorCode.INTERNAL_ERROR,
                LLMServiceConstants.ErrorMessages.LLM_RESPONSE_FORMAT_ERROR
            );
        }
    }

    /**
     * 从output字段中提取JSON内容
     * 
     * @param output 原始output内容
     * @return 清理后的JSON字符串
     */
    private String extractJsonFromOutput(String output) {
        // 去除```json和```标记
        String cleanJson = output.replaceFirst("```json\\s*", "").replaceAll("```\\s*$", "").trim();
        log.debug("提取的JSON内容: {}", cleanJson);
        return cleanJson;
    }

    /**
     * 映射LLM返回的风险等级到系统风险等级
     * 
     * @param llmRiskLevel LLM返回的风险等级
     * @return 系统风险等级
     */
    private String mapRiskLevel(String llmRiskLevel) {
        if (StrUtil.isBlank(llmRiskLevel)) {
            return LLMServiceConstants.DefaultValues.DEFAULT_RISK_LEVEL;
        }

        // 映射LLM返回的风险等级
        switch (llmRiskLevel.trim()) {
            case LLMServiceConstants.RiskMapping.LLM_LOW_RISK:
                return RiskAssessmentConstants.RiskLevel.LOW_RISK;
            case LLMServiceConstants.RiskMapping.LLM_MEDIUM_RISK:
                return RiskAssessmentConstants.RiskLevel.MEDIUM_RISK;
            case LLMServiceConstants.RiskMapping.LLM_HIGH_RISK:
                return RiskAssessmentConstants.RiskLevel.HIGH_RISK;
            default:
                log.warn("未知的LLM风险等级: {}, 使用默认风险等级", llmRiskLevel);
                return LLMServiceConstants.DefaultValues.DEFAULT_RISK_LEVEL;
        }
    }

    /**
     * 生成缓存key
     * 使用MD5摘要确保相同内容产生相同key，不同内容产生不同key
     *
     * @param conversationContent 对话内容
     * @return 缓存key
     */
    public static String generateCacheKey(String conversationContent) {
        if (conversationContent == null || conversationContent.trim().isEmpty()) {
            return "empty_content";
        }

        try {
            // 使用MD5生成摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(conversationContent.getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            // 添加前缀和内容长度，便于调试
            return String.format("llm_risk_%d_%s", conversationContent.length(), sb);

        } catch (NoSuchAlgorithmException e) {
            // MD5算法不存在的情况下，使用内容长度+hashCode作为fallback
            log.warn("MD5算法不可用，使用fallback方案生成缓存key", e);
            return String.format("llm_risk_fallback_%d_%d",
                conversationContent.length(),
                Math.abs(conversationContent.hashCode()));
        }
    }
}
