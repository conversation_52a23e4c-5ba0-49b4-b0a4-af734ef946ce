package com.baidu.keyue.deepsight.service.consumer;

import cn.hutool.core.collection.CollUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableRecordMsgDTO;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordResp;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.datamanage.AiobRobotVersionService;
import com.baidu.keyue.deepsight.service.datamanage.TableContentService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.service.tenant.impl.TenantInfoServiceImpl;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.kybase.commons.utils.HttpUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @className AIConversationDebugWorker
 * @description 外呼灵活画布debug数据
 * @date 2025/6/6 14:17
 */
@Slf4j
@Component
public class AIConversationDebugWorker extends AbstractDataSyncWorker {

    @Autowired
    private TableRecordCommonService commonService;

    @Resource
    private TenantInfoService tenantInfoService;

    @Resource
    private AiobRobotVersionService aiobRobotVersionService;

    @Autowired
    private AiobSOPService sopService;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Value("${kafka.topics.dataSync:deep_sight_data_sync}")
    private String topic;

    @Value("${aiob.url:http://127.0.0.1:8080}")
    private String aiobUrl;
    @Value("${aiob.debugPath:aaa}")
    private String debugPath;

    @Value("${aiob.debugPathRetry:3}")
    private Integer debugPathRetry;

    /**
     * TODO 临时开关
     */
    @Value("${temp.kafka.switch:false}")
    private boolean kafkaFaqSwitch;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public AIConversationDebugWorker(TableContentService contentService, TenantInfoServiceImpl tenantInfoService) {
        super(contentService, tenantInfoService);
    }


    @Override
    protected void processData(String data) {
        try {
            if (data.startsWith("\"") && data.endsWith("\"")) {
                data = data.substring(1, data.length() - 1);
            }
            String json = StringEscapeUtils.unescapeJson(data); // apache commons-text
            Map<String, String> map = objectMapper.readValue(json, Map.class);
            String queryId = map.get("queryId");
            if (StringUtils.isEmpty(map.get("tenantId"))) {
                log.debug("aiobDebug queryId {} of tenantId is empty", queryId);
                return;
            }
            String sessionId = map.get("sessionId");
            Long tenantId = Long.valueOf(map.get("tenantId"));            // 租户不存在，直接跳过
            TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(String.valueOf(tenantId));
            if (null == tenantInfo) {
                log.debug("aiobDebug queryTenantInfo got empty tenantId: {}, sessionId: {}", tenantId, sessionId);
                return;
            }
            List<String> intentList = new ArrayList<>();
            if (!ObjectUtils.isEmpty(map.get("intent"))) {
                List<Map<String, Object>> intent = objectMapper.readValue(map.get("intent"), new TypeReference<List<Map<String, Object>>>() {
                });
                intent.stream().forEach(item -> {
                    intentList.add((String) item.get("nameZh"));
                });
            }
            String agentId = map.get("agentId");
            String versionId = map.get("versionId");
            Map<String, Object> dataMap = getRobotInfo(tenantId, agentId, sessionId);
            List<Map<String, Object>> result = objectMapper.readValue(map.get("answer"), new TypeReference<List<Map<String, Object>>>() {
            });
            for (Map<String, Object> item : result) {
                String blockId = "";
                if (Objects.nonNull(item.get("blockId"))) {
                    blockId = (String) item.get("blockId");
                } else {
                    blockId = (String) item.get("nodeId");
                }
                if (StringUtils.isBlank(blockId)) {
                    continue;
                }
                Map<String, Object> res = new HashMap<>();
                res.put("queryId", queryId);
                res.put("sessionId", sessionId);
                res.put("agent_id", agentId);
                res.put("version_id", versionId);
                res.put("topicId", (String) item.get("topicId"));
                res.put("nodeId", blockId);
                res.put("chunkId", item.get("chunkId"));
                res.put("intent", intentList);
                // 灵活画布解析知识
                if (kafkaFaqSwitch){
                    getProcessKnowLedgeData(res, result);
                }
                if (CollUtil.isNotEmpty(dataMap)) {
                    res.put("robot_id", (String) dataMap.get("robotId"));
                    res.put("robot_ver", String.valueOf((Long) dataMap.get("botVersionId")));
                    TableRecordMsgDTO recordMsg = new TableRecordMsgDTO();
                    recordMsg.setCode(TenantUtils.generateAiobDebugTableName(String.valueOf(tenantId)));
                    recordMsg.setData(res);
                    String msg = objectMapper.writeValueAsString(recordMsg);
                    kafkaTemplate.send(topic, msg);
                } else {
                    log.warn("aiobDebug data discard sessionId: {}", sessionId);
                }
            }
            addRobotVersion(map);
        } catch (Exception e) {
            log.error("aiobDebug process error. err: ", e);
        }
    }

    @SuppressWarnings("unchecked")
    private static void getProcessKnowLedgeData(Map<String, Object> row, List<Map<String, Object>> result) {

        if (CollectionUtils.isEmpty(result)){
            return;
        }

        List<String> allQuestions = new ArrayList<>();
        List<String> allDirNames = new ArrayList<>();

        for (Map<String, Object> chunk : result) {
            String debugReplyType = String.valueOf(chunk.get("debugReplyType"));
            String debugReplyTypeV2 = String.valueOf(chunk.get("debugReplyTypeV2"));
            // 回复答案类型不是faq且非大模型回复节点 generation，跳过
            if (!"faq".equalsIgnoreCase(debugReplyType)
                    && !"generation".equalsIgnoreCase(debugReplyTypeV2)) {
                continue;
            }

            Map<String, Object> reply = (Map<String, Object>) chunk.get("reply");
            if (reply == null) {
                continue;
            }

            List<Map<String, Object>> faqSearchList = (List<Map<String, Object>>) reply.get("faqSearch");
            if (faqSearchList == null || faqSearchList.isEmpty()) {
                continue;
            }

            for (Map<String, Object> faqMap : faqSearchList) {
                allQuestions.add(String.valueOf(faqMap.get("question")));
                allDirNames.add(String.valueOf(faqMap.get("dirName")));
            }
        }
        row.put("faq", allQuestions);
        row.put("dirName", allDirNames);
    }

    /**
     * 获取机器人信息，有自动重试
     *
     * @param tenantId
     * @param agentId
     * @param sessionId
     * @return
     */
    public Map<String, Object> getRobotInfo(Long tenantId, String agentId, String sessionId) {
        Map<String, Object> dataMap = new HashMap<>();
        String url = aiobUrl + "/" + debugPath;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("agent-id", String.valueOf(tenantId));
        headers.put(Constants.ACCESS_TOKEN, Constants.ACCESS_TOKEN_VALUE);
        for (int i = 0; i < debugPathRetry; i++) {
            try {
                String response = HttpUtil.get(url + "?agentId=" + agentId, headers);
                Map<String, Object> bodyMap = objectMapper.readValue(response, Map.class);
                return (Map<String, Object>) bodyMap.get("data");
            } catch (Exception e) {
                log.error("aiobDebug debugPath failed url: {}, sessionId: {}, err: ", url, sessionId, e);
            }
        }
        return dataMap;
    }

    public void addRobotVersion(Map<String, String> map) {
        // 检查 map 中是否存在 robotScene 键且其值为 5
        if (ObjectUtils.isNotEmpty(map.get("versionId")) && ObjectUtils.isNotEmpty(map.get("agentId"))
                && ObjectUtils.isNotEmpty(map.get("tenantId"))) {
            // 获取 versionId
            String versionId = map.get("versionId");
            // 获取 agentId
            String agentId = map.get("agentId");
            // 获取 tenantId
            String tenantId = map.get("tenantId");

            // 检查 aiobRobotVersionService 中是否存在该版本的机器人
            if (aiobRobotVersionService.getAiobRobotVersion(tenantId, agentId, versionId)) {
                // 如果存在，则直接返回
                return;
            }
            String botVersionName = "";
            String publishTime = "";
            AiobDiagramVersionRecordResp aiobDiagramVersionRecordResp = sopService.getDiagramVersions(agentId, versionId);
            // 获取aiobDiagramVersionRecordResp里id为versionId的记录，并获取versionName，publishTime
            for (AiobDiagramVersionRecordResp.AiobDiagramRecordData item : aiobDiagramVersionRecordResp.getData()) {
                if (Objects.equals(item.getId(), versionId)) {
                    botVersionName = item.getVersionName();
                    publishTime = item.getCreateTime();
                    break;
                }
            }
            if (StringUtils.isEmpty(botVersionName) || StringUtils.isEmpty(publishTime)) {
                return;
            }
            // 如果不存在，则保存该版本的机器人信息
            aiobRobotVersionService.saveDiagramRobotVersion(tenantId, agentId, versionId, botVersionName, publishTime);
        }
    }

    /**
     * 从 kafka 监听器消费消息，处理历史记录日志数据
     *
     * @param msg 消息
     */
    @KafkaListener(topics = "${deepSight.kafka.topic.debug:deep_sight_aiob_debug_data_sync}")
    public void conversationDebugLogConsumer(String msg) {
        log.debug("aiobDebug receive msg is {}", msg);
        processData(msg);
    }


}
