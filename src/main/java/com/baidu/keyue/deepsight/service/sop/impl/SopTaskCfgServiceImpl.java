package com.baidu.keyue.deepsight.service.sop.impl;

import com.alibaba.fastjson.JSON;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.AiobRobotTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigAssistMetricEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigMainMetricEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigWarningThresholdEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.dial.RobotInfoResponse;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisCompletionTimeResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskCfgRequestBase;
import com.baidu.keyue.deepsight.models.sop.SopTaskCfgStatusEnum;
import com.baidu.keyue.deepsight.models.sop.SopTaskCreateRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskQueryRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskUpdateRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfg;
import com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfgCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.SopTaskCfgMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.SopUserConfigMapper;
import com.baidu.keyue.deepsight.service.sop.SopAnalysisResultService;
import com.baidu.keyue.deepsight.service.sop.SopTaskCfgService;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.models.sop.SopUserConfigUpdateRequest;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.min;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SOP离线分析任务配置服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SopTaskCfgServiceImpl implements SopTaskCfgService {

    private final SopTaskCfgMapper sopTaskCfgMapper;
    private final AiobSOPService aiobSOPService;
    private final SopUserConfigMapper sopUserConfigMapper;
    private final SopAnalysisResultService sopAnalysisResultService;
    private final DorisService dorisService;

    @Override
    public Long createOfflineTask(String tenantId, SopTaskCreateRequest request, String creator) {

        // 检查是否已存在相同配置的任务
        if (existsSameTask(-1L, -1L, tenantId, request)) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "已存在相同配置的离线分析任务");
        }

        SopTaskCfg sopTaskCfg = new SopTaskCfg();
        sopTaskCfg.setTenantId(tenantId);
        sopTaskCfg.setTaskId(request.getTaskId());
        sopTaskCfg.setRobotId(request.getRobotId());
        sopTaskCfg.setRobotScene(request.getRobotScene());
        sopTaskCfg.setTaskName(request.getTaskName());
        sopTaskCfg.setRobotName(request.getRobotName());
        sopTaskCfg.setRobotIcon(request.getRobotIcon());

        // 处理机器人版本列表，转为JSON字符串
        if (!CollectionUtils.isEmpty(request.getRobotVersion())) {
            List<String> robotVersionIds = request.getRobotVersion().stream()
                    .map(RobotInfoResponse::getRobotId)
                    .collect(Collectors.toList());
            List<String> robotVersionNames = request.getRobotVersion().stream()
                    .map(RobotInfoResponse::getRobotName)
                    .collect(Collectors.toList());
            sopTaskCfg.setRobotVersion(JSON.toJSONString(robotVersionIds));
            sopTaskCfg.setRobotVersionName(JSON.toJSONString(robotVersionNames));
        } else {
            sopTaskCfg.setRobotVersion("[]");
            sopTaskCfg.setRobotVersionName("[]");
        }

        sopTaskCfg.setIsAutoAnswer(request.getIsAutoAnswer());
        sopTaskCfg.setCreator(creator);
        sopTaskCfg.setModifier(creator);

        // 处理标签列表，转为JSON字符串
        if (!CollectionUtils.isEmpty(request.getTags())) {
            sopTaskCfg.setTags(JSON.toJSONString(request.getTags()));
        } else {
            sopTaskCfg.setTags("[]");
        }

        // 默认状态为执行中
        sopTaskCfg.setStatus(SopTaskCfgStatusEnum.RUNNING.getCode());
        sopTaskCfg.setAnalysisCount(request.getAnalysisCount());
        sopTaskCfg.setCreateTime(new Date());
        sopTaskCfg.setUpdateTime(new Date());

        // 初始化用户配置
        SopUserConfig userConfig = aiobSOPService.createUserConfig(tenantId, request.getUserConfig());
        if (!ObjectUtils.isEmpty(userConfig)) {
            sopTaskCfg.setUserConfigId(userConfig.getId());
        }

        // 处理快捷场景的confirmConfig
        if (request.getConfirmConfig() != null) {
            // 保存节点确认配置到SOPService
            aiobSOPService.confirmNode(tenantId, request.getTaskId(), request.getConfirmConfig().getRule(), 
                    request.getConfirmConfig().getMarkdown(), request.getRobotVersion().get(0).getRobotId());
        }

        int result = sopTaskCfgMapper.insertSelective(sopTaskCfg);
        if (result <= 0) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "创建离线分析任务失败");
        }


        return sopTaskCfg.getId();
    }

    @Override
    public SopAnalysisCompletionTimeResponse getTaskCompletionTime(String tenantId, SopTaskCfgRequestBase request) {

        StringBuilder countSql =
                new StringBuilder("SELECT count(1) FROM ").append(TenantUtils.generateSopAnalysisResultTableName(tenantId));
        if (Objects.equals((byte) 1, request.getIsAutoAnswer())) {
            countSql.append(" WHERE (is_auto_answer != 1 OR is_auto_answer IS NULL)");
        }
        long count = dorisService.getCount(countSql.toString());

        // 每条数据的平均处理时间 = 10min / 100000 = 0.0001 min
        double minutes = count / 10000.0;
        long estimatedMinutes = (long) Math.ceil(minutes);

        // 转换为小时和分钟
        long hours = estimatedMinutes / 60;
        long mins = estimatedMinutes % 60;

        String estimateTime = hours > 0
                ? String.format("%d小时%d分钟", hours, mins)
                : String.format("%d分钟", mins);

        return SopAnalysisCompletionTimeResponse.builder().analysisCount(count).estimatedTime(estimateTime).build();
    }

    @Override
    public BasePageResponse.Page<SopTaskResponse> queryOfflineTasks(String tenantId, SopTaskQueryRequest request) {
        log.debug("分页查询SOP离线分析任务, tenantId: {}, request: {}", tenantId, JSON.toJSONString(request));


        SopTaskCfgCriteria criteria = new SopTaskCfgCriteria();
        SopTaskCfgCriteria.Criteria criteriaCondition = criteria.createCriteria();
        criteriaCondition.andTenantIdEqualTo(tenantId);



        if (StringUtils.hasText(request.getRobotId())) {
            criteriaCondition.andRobotIdEqualTo(request.getRobotId());
        }


        if (request.getStatus() != null) {
            criteriaCondition.andStatusEqualTo(request.getStatus());
        }

        if (StringUtils.hasText(request.getKeyword())) {
            criteriaCondition.andTaskNameContainsByLocate(request.getKeyword());
        }

        criteria.setOrderByClause("create_time desc");
        long count = sopTaskCfgMapper.countByExample(criteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }

        // 设置分页参数
        PageHelper.startPage(request.getPageNo(), request.getPageSize());
        List<SopTaskCfg> taskList = sopTaskCfgMapper.selectByExample(criteria);

        // 转换为响应对象
        List<SopTaskResponse> responseList = taskList.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());


        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, responseList);
    }

    @Override
    public SopTaskResponse getOfflineTaskById(String tenantId, Long taskId) {

        List<SopTaskCfg> sopTaskCfg = getSopTaskCfgList(tenantId, taskId);
        return convertToResponse(sopTaskCfg.get(0));
    }

    private List<SopTaskCfg> getSopTaskCfgList(String tenantId, Long taskId) {
        SopTaskCfgCriteria criteria = new SopTaskCfgCriteria();
        criteria.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(taskId);
        List<SopTaskCfg> sopTaskCfg = sopTaskCfgMapper.selectByExample(criteria);
        if (CollectionUtils.isEmpty(sopTaskCfg)) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "分析任务不存在");
        }
        return sopTaskCfg;
    }

    @Override
    public void updateTaskStatus(String tenantId, Long taskId, Byte status, String modifier) {

        SopTaskCfg sopTaskCfg = getTaskByIdAndTenant(tenantId, taskId);
        if (SopTaskCfgStatusEnum.RUNNING.getCode().equals(sopTaskCfg.getStatus())) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "正在执行的任务不能更新状态");
        }

        SopTaskCfg updateRecord = new SopTaskCfg();
        updateRecord.setId(taskId);
        updateRecord.setStatus(status);
        updateRecord.setModifier(modifier);
        updateRecord.setUpdateTime(new Date());

        int result = sopTaskCfgMapper.updateByPrimaryKeySelective(updateRecord);
        if (result <= 0) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.INTERNAL_ERROR, "更新任务状态失败");
        }

    }

    @Override
    public void updateTaskMessage(String tenantId, Long taskId, String taskMsg) {
        SopTaskCfg sopTaskCfg = getTaskByIdAndTenant(tenantId, taskId);

        SopTaskCfg updateRecord = new SopTaskCfg();
        updateRecord.setId(taskId);
        updateRecord.setTaskMsg(taskMsg);
        updateRecord.setUpdateTime(new Date());
        updateRecord.setId(sopTaskCfg.getId());

        int result = sopTaskCfgMapper.updateByPrimaryKeySelective(updateRecord);
        if (result <= 0) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.INTERNAL_ERROR, "更新任务执行信息失败");
        }

    }

    @Override
    public void updateOfflineTask(String tenantId, SopTaskUpdateRequest request, String modifier) {


        List<SopTaskCfg> sopTaskCfgs = getSopTaskCfgList(tenantId, request.getId());
        SopTaskCfg sopTaskCfg = sopTaskCfgs.stream()
                .findFirst()
                .orElseThrow(() -> new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "分析任务不存在"));

        if (existsSameTask(request.getId(), request.getUserConfigId(), tenantId, request)) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "已存在相同配置的离线分析任务");

        }
        // 检查任务状态，只有成功或执行失败的任务可以编辑
        if (SopTaskCfgStatusEnum.RUNNING.getCode().equals(sopTaskCfg.getStatus())) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "正在执行的任务不能编辑");
        }

        SopTaskCfg updateRecord = new SopTaskCfg();
        updateRecord.setId(request.getId());

        // 更新任务名称和机器人名称
        if (StringUtils.hasText(request.getTaskName())) {
            updateRecord.setTaskName(request.getTaskName());
        }

        // 更新机器人版本列表
        if (!CollectionUtils.isEmpty(request.getRobotVersion())) {
            List<String> robotVersionIds = request.getRobotVersion().stream()
                    .map(RobotInfoResponse::getRobotId)
                    .collect(Collectors.toList());
            List<String> robotVersionNames = request.getRobotVersion().stream()
                    .map(RobotInfoResponse::getRobotName)
                    .collect(Collectors.toList());
            updateRecord.setRobotVersion(JSON.toJSONString(robotVersionIds));
            updateRecord.setRobotVersionName(JSON.toJSONString(robotVersionNames));
        }

        // 更新是否自动应答
        if (request.getIsAutoAnswer() != null) {
            updateRecord.setIsAutoAnswer(request.getIsAutoAnswer());
        }

        // 更新标签列表
        if (!CollectionUtils.isEmpty(request.getTags())) {
            updateRecord.setTags(JSON.toJSONString(request.getTags()));
        }

        updateRecord.setModifier(modifier);
        updateRecord.setUpdateTime(new Date());

        sopTaskCfgMapper.updateByPrimaryKeySelective(updateRecord);

        // 同步更新用户配置
        if (request.getUserConfig() != null) {
            String userId = WebContextHolder.getUserId();
            SopUserConfigMainMetricEnum mainMetric =
                    SopUserConfigMainMetricEnum.codeOf(request.getUserConfig().getMainMetric());
            SopUserConfigAssistMetricEnum assistMetric =
                    SopUserConfigAssistMetricEnum.codeOf(request.getUserConfig().getAssistMetric());
            SopUserConfigWarningThresholdEnum threshold =
                    SopUserConfigWarningThresholdEnum.codeOf(request.getUserConfig().getWarningThreshold());

            SopUserConfig userConfig = new SopUserConfig();
            userConfig.setCoreMetric(mainMetric.getCode().byteValue());
            userConfig.setAssistMetric(assistMetric.getCode().byteValue());
            userConfig.setWarningThreshold(threshold.getCode());
            userConfig.setModifier(userId);
            userConfig.setUpdateTime(new Date());
            userConfig.setId(sopTaskCfg.getUserConfigId()); // 修正：使用原任务的userConfigId
            userConfig.setTenantId(tenantId);
            sopUserConfigMapper.updateByPrimaryKeySelective(userConfig);
        }

        // 处理快捷场景的confirmConfig更新
        if (request.getConfirmConfig() != null && !CollectionUtils.isEmpty(request.getRobotVersion())) {
            // 更新节点确认配置
            aiobSOPService.confirmNode(tenantId, request.getTaskId(), request.getConfirmConfig().getRule(), 
                    request.getConfirmConfig().getMarkdown(), request.getRobotVersion().get(0).getRobotId());
        }


    }

    @Override
    public void deleteOfflineTask(String tenantId, Long taskId) {
        SopTaskCfg taskByIdAndTenant = getTaskByIdAndTenant(tenantId, taskId);
        int result = sopTaskCfgMapper.deleteByPrimaryKey(taskByIdAndTenant.getId());
        if (result <= 0) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.INTERNAL_ERROR, "删除任务失败");
        }

        // TODO 同步删除分析结果

    }

    @Override
    public boolean cancelRunningTask(String tenantId, Long taskId, String operator) {
        return false;
    }

    @Override
    public boolean existsSameTask(Long id, Long userConfigId, String tenantId, SopTaskCfgRequestBase requestBase) {
        // 先检查基础任务配置是否重复
        SopTaskCfgCriteria taskCfgCriteria = new SopTaskCfgCriteria();
        SopTaskCfgCriteria.Criteria cfgCriteriaCriteria = taskCfgCriteria.createCriteria();
        cfgCriteriaCriteria
                .andTenantIdEqualTo(tenantId)
                .andTaskIdEqualTo(requestBase.getTaskId())
                .andRobotIdEqualTo(requestBase.getRobotId())
                .andRobotSceneEqualTo(requestBase.getRobotScene());

        // 排除当前任务（编辑时）
        if (id != null && id > 0L) {
            cfgCriteriaCriteria.andIdNotEqualTo(id);
        }

        List<SopTaskCfg> sopTaskCfgs = sopTaskCfgMapper.selectByExample(taskCfgCriteria);
        if (CollectionUtils.isEmpty(sopTaskCfgs)) {
            return false;
        }

        // 构建请求的机器人版本JSON字符串
        String requestRobotVersionJson = "[]";
        if (!CollectionUtils.isEmpty(requestBase.getRobotVersion())) {
            List<String> robotVersionIds = requestBase.getRobotVersion().stream()
                    .map(RobotInfoResponse::getRobotId)
                    .collect(Collectors.toList());
            requestRobotVersionJson = JSON.toJSONString(robotVersionIds);
        }

        // 构建请求的标签JSON字符串
        String requestTagsJson = "[]";
        if (!CollectionUtils.isEmpty(requestBase.getTags())) {
            requestTagsJson = JSON.toJSONString(requestBase.getTags());
        }

        final String finalRobotVersionJson = requestRobotVersionJson;
        final String finalTagsJson = requestTagsJson;

        // 检查是否存在完全相同的任务配置
        boolean taskConfigExists = sopTaskCfgs.stream().anyMatch(taskCfg -> 
                taskCfg.getIsAutoAnswer().equals(requestBase.getIsAutoAnswer())
                && taskCfg.getRobotVersion().equals(finalRobotVersionJson)
                && taskCfg.getTags().equals(finalTagsJson));

        if (!taskConfigExists) {
            return false;
        }

        // 检查用户配置是否重复
        if (requestBase.getUserConfig() == null) {
            return true; // 如果没有用户配置，直接返回任务配置重复
        }

        SopUserConfigCriteria example = new SopUserConfigCriteria();
        SopUserConfigCriteria.Criteria criteria = example.createCriteria();
        
        // 排除当前用户配置（编辑时）
        if (userConfigId != null && userConfigId > 0L) {
            criteria.andIdNotEqualTo(userConfigId);
        }

        criteria.andTenantIdEqualTo(tenantId)
                .andTaskIdEqualTo(requestBase.getTaskId())
                .andAssistMetricEqualTo(SopUserConfigAssistMetricEnum.codeOf(requestBase.getUserConfig().getAssistMetric()).getCode().byteValue())
                .andCoreMetricEqualTo(SopUserConfigMainMetricEnum.codeOf(requestBase.getUserConfig().getMainMetric()).getCode().byteValue())
                .andWarningThresholdEqualTo(SopUserConfigWarningThresholdEnum.codeOf(requestBase.getUserConfig().getWarningThreshold()).getCode())
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());

        long userConfigCount = sopUserConfigMapper.countByExample(example);
        return userConfigCount > 0;
    }

    @Override
    public List<SopTaskResponse> getTasksByStatus(String tenantId, Byte status) {

        if (tenantId == null) {
            // 查询所有租户的任务
            SopTaskCfgCriteria criteria = new SopTaskCfgCriteria();
            SopTaskCfgCriteria.Criteria criteriaCondition = criteria.createCriteria();

            if (status != null) {
                criteriaCondition.andStatusEqualTo(status);
            }

            criteria.setOrderByClause("create_time desc");

            List<SopTaskCfg> taskList = sopTaskCfgMapper.selectByExample(criteria);

            return taskList.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } else {
            // 查询指定租户的任务
            List<SopTaskCfg> taskList = sopTaskCfgMapper.selectByTenantAndStatus(tenantId, status);

            return taskList.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 转换实体对象为响应对象
     */
    private SopTaskResponse convertToResponse(SopTaskCfg sopTaskCfg) {
        SopTaskResponse response = new SopTaskResponse();
        response.setId(sopTaskCfg.getId());
        response.setTaskId(sopTaskCfg.getTaskId());
        response.setRobotId(sopTaskCfg.getRobotId());
        response.setRobotScene(sopTaskCfg.getRobotScene());
        response.setRobotSceneDesc(getRobotSceneDesc(sopTaskCfg.getRobotScene()));

        // 解析机器人版本列表
        if (StringUtils.hasText(sopTaskCfg.getRobotVersion())) {
            try {
                List<String> robotVersion = JSON.parseArray(sopTaskCfg.getRobotVersion(), String.class);
                List<String> robotVersionName = JSON.parseArray(sopTaskCfg.getRobotVersionName(), String.class);

                List<RobotInfoResponse> robotInfos = new ArrayList<>();
                if (!CollectionUtils.isEmpty(robotVersion) && !CollectionUtils.isEmpty(robotVersionName)) {
                    for (int i = 0; i < robotVersion.size(); i++) {
                        RobotInfoResponse info = new RobotInfoResponse();
                        info.setRobotId(robotVersion.get(i));
                        info.setRobotName(i < robotVersionName.size() ? robotVersionName.get(i) : null);
                        robotInfos.add(info);
                    }
                }

                response.setRobotVersion(robotInfos);
            } catch (Exception e) {
                log.warn("解析机器人版本列表失败: {}", sopTaskCfg.getRobotVersion(), e);
                response.setRobotVersion(new ArrayList<>());
            }
        } else {
            response.setRobotVersion(new ArrayList<>());
        }

        response.setIsAutoAnswer(sopTaskCfg.getIsAutoAnswer());
        response.setCreator(sopTaskCfg.getCreator());
        response.setCreateTime(sopTaskCfg.getCreateTime());
        response.setUpdateTime(sopTaskCfg.getUpdateTime());

        // 解析标签列表
        if (StringUtils.hasText(sopTaskCfg.getTags())) {
            try {
                response.setTags(JSON.parseArray(sopTaskCfg.getTags(), String.class));
            } catch (Exception e) {
                log.warn("解析标签列表失败: {}", sopTaskCfg.getTags(), e);
                response.setTags(new ArrayList<>());
            }
        } else {
            response.setTags(new ArrayList<>());
        }

        response.setStatus(sopTaskCfg.getStatus());
        response.setStatusDesc(getStatusDesc(sopTaskCfg.getStatus()));
        response.setTaskMsg(sopTaskCfg.getTaskMsg());
        response.setTaskName(sopTaskCfg.getTaskName());
        response.setRobotName(sopTaskCfg.getRobotName());
        
        // 设置新增字段
        response.setRobotIcon(sopTaskCfg.getRobotIcon());
        response.setAnalysisCount(sopTaskCfg.getAnalysisCount() != null ? sopTaskCfg.getAnalysisCount() : 0);
        

        // 查询用户配置信息
        SopUserConfigCriteria configCriteria = new SopUserConfigCriteria();
        configCriteria.createCriteria().andIdEqualTo(sopTaskCfg.getUserConfigId()).andTenantIdEqualTo(sopTaskCfg.getTenantId()).
                andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<SopUserConfig> sopUserConfigs = sopUserConfigMapper.selectByExample(configCriteria);
        SopUserConfig userConfig = sopUserConfigs.stream()
                .findFirst().stream().findFirst().orElse(null);

        if (!ObjectUtils.isEmpty(userConfig)) {
            SopUserConfigUpdateRequest sopUserConfigUpdateRequest = new SopUserConfigUpdateRequest();
            sopUserConfigUpdateRequest.setMainMetric(Integer.valueOf(userConfig.getCoreMetric()));
            sopUserConfigUpdateRequest.setAssistMetric(Integer.valueOf(userConfig.getAssistMetric()));
            sopUserConfigUpdateRequest.setWarningThreshold(userConfig.getWarningThreshold());
            response.setUserConfig(sopUserConfigUpdateRequest);
        } else {
            response.setUserConfig(null);
        }

        return response;
    }

    /**
     * 获取机器人场景描述
     */
    private String getRobotSceneDesc(Byte robotScene) {
        AiobRobotTypeEnum robotTypeEnum = AiobRobotTypeEnum.fromCode(robotScene);
        return robotTypeEnum != null ? robotTypeEnum.getDesc() : "未知";
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Byte status) {
        SopTaskCfgStatusEnum statusEnum = SopTaskCfgStatusEnum.fromCode(status);
        return statusEnum != null ? statusEnum.getDesc() : "未知";
    }

    /**
     * 根据任务ID和租户ID查询任务，如果不存在则抛出异常
     */
    private SopTaskCfg getTaskByIdAndTenant(String tenantId, Long taskId) {
        SopTaskCfg sopTaskCfg = sopTaskCfgMapper.selectByPrimaryKey(taskId);
        if (sopTaskCfg == null || !tenantId.equals(sopTaskCfg.getTenantId())) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.NOT_FOUND, "任务不存在");
        }
        return sopTaskCfg;
    }
}