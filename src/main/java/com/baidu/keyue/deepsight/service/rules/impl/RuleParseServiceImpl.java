package com.baidu.keyue.deepsight.service.rules.impl;

import cn.hutool.core.collection.CollUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.common.utils.KieUtil;
import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.baidu.keyue.deepsight.enums.LogicEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelField;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelFieldCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelFieldMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.utils.AliasUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.google.common.collect.Lists;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @className: RuleParseServiceImpl
 * @description: 规则解析为 doris sql 服务实现类
 * @author: wangzhongcheng
 * @date: 2024/12/21 16:56
 */
@Slf4j
@Setter
@Service
public class RuleParseServiceImpl implements RuleParseService {

    @Autowired
    private DorisService dorisService;

    /**
     * 字段信息mapper
     */
    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    /**
     * 数据表信息mapper
     */
    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    /**
     * 标签字段mapper
     */
    @Autowired
    private LabelFieldMapper labelFieldMapper;

    @Autowired
    private DorisConfiguration dorisConfiguration;

    @Override
    public DqlParseResult parseRuleGroup(RuleGroup ruleGroup, AtomicInteger alias) {
        ruleGroup = RuleGroup.clone(ruleGroup);

        DqlParseResult result = null;
        List<RuleGroup> lastedRuleGroups = ruleGroup.getRuleGroups();
        // 对最后一层规则组进行合并 与 逻辑关系组装；
        if (ruleGroup.getRelation() == LogicEnum.AND) {
            lastedRuleGroups = mergeSameLogicRuleGroup(ruleGroup.getRuleGroups());
        }

        for (RuleGroup group : lastedRuleGroups) {
            DqlParseResult curDqlParseResult = parseLastRuleGroup(group, alias);
            if (result == null) {
                result = curDqlParseResult;
                if (result.getWhere().size() > 1) {
                    // 组装where条件
                    String where = String.join(curDqlParseResult.getRelationSplit(), result.getWhere());
                    where = String.format("(%s)", where);
                    result.getWhere().clear();
                    result.getWhere().add(where);
                }
                if (result.getHaving().size() > 1) {
                    String having = String.join(curDqlParseResult.getRelationSplit(), result.getHaving());
                    having = String.format("(%s)", having);
                    result.getHaving().clear();
                    result.getHaving().add(having);
                }
            } else {
                // 组装where条件
                String addWhere = String.format("%s IN (%s)", result.getSelect(), curDqlParseResult.parseDorisSql());
                result.getWhere().add(addWhere);
                result.addFieldSchema(curDqlParseResult);
            }
        }
        result.setRelation(ruleGroup.getRelation());
        log.info("parseRuleGroup result:\n{}", result.parseDorisSql());
        return result;
    }

    /**
     * 合并同一逻辑关系规则组
     */
    private List<RuleGroup> mergeSameLogicRuleGroup(List<RuleGroup> lastRuleGroups) {
        List<RuleGroup> mergeResult = new ArrayList<>();
        Map<LogicEnum, List<RuleGroup>> lastRuleGroupMap = lastRuleGroups
                .stream()
                .collect(Collectors.groupingBy(RuleGroup::getRelation));

        for (LogicEnum logic : lastRuleGroupMap.keySet()) {
            RuleGroup ruleGroup = new RuleGroup();
            ruleGroup.setRelation(logic);
            ruleGroup.setRuleNodes(Lists.newArrayList());
            lastRuleGroupMap.get(logic).forEach(item -> {
                ruleGroup.getRuleNodes().addAll(item.getRuleNodes());
            });
            mergeResult.add(ruleGroup);
        }
        mergeResult.forEach(ruleGroup -> {
            List<RuleNode> mergeSameTableRuleNode = mergeSameTableRuleNode(ruleGroup.getRuleNodes());
            ruleGroup.setRuleNodes(mergeSameTableRuleNode);
        });
        return mergeResult;
    }

    /**
     * 同一表的过滤条件能合并
     */
    private List<RuleNode> mergeSameTableRuleNode(List<RuleNode> ruleNodes) {
        // 组装同一数据表的规则节点，group by 数据表
        Map<String, RuleNode> ruleNodeTableMap = new HashMap<>();
        for (RuleNode needMergeRuleNode : ruleNodes) {
            RuleNode waitMergeRuleNode = null;
            boolean isNeedMerge = false;
            switch (needMergeRuleNode.getType()) {
                case USER, LABEL -> {
                    isNeedMerge = ruleNodeTableMap.containsKey(needMergeRuleNode.getType().getCode());
                    if (isNeedMerge) {
                        waitMergeRuleNode = ruleNodeTableMap.get(needMergeRuleNode.getType().getCode());
                    } else {
                        ruleNodeTableMap.put(needMergeRuleNode.getType().getCode(), needMergeRuleNode);
                    }
                }
                case DATASET -> {
                    isNeedMerge = ruleNodeTableMap.containsKey(String.valueOf(needMergeRuleNode.getDataTableId()));
                    if (isNeedMerge) {
                        waitMergeRuleNode = ruleNodeTableMap.get(String.valueOf(needMergeRuleNode.getDataTableId()));
                    } else {
                        ruleNodeTableMap.put(String.valueOf(needMergeRuleNode.getDataTableId()), needMergeRuleNode);
                    }
                }
            }
            if (isNeedMerge && Objects.nonNull(waitMergeRuleNode)) {
                waitMergeRuleNode.getFilters().addAll(needMergeRuleNode.getFilters());
            }
        }
        return ruleNodeTableMap.values().stream().toList();
    }

    /**
     * 解析最后一层规则组
     *
     * @param ruleGroup
     * @return
     */
    private DqlParseResult parseLastRuleGroup(RuleGroup ruleGroup, AtomicInteger aliasNum) {
        String relationSplit = ruleGroup.getRelation() == LogicEnum.AND ? " AND " : " OR ";
        DqlParseResult result = null;
        for (RuleNode ruleNode : ruleGroup.getRuleNodes()) {
            checkRuleNode(ruleNode);
            DqlParseResult curDqlParseResult = parseRuleNode(ruleNode);
            String tableName = curDqlParseResult.getFrom();
            String userRelField = String.format(Constants.DORIS_FIELD_TEM, tableName, Constants.TABLE_USER_ONE_ID);
            curDqlParseResult.setSelect(userRelField);
            // 抽样计算需要设置别名
            curDqlParseResult.aliasTable(
                    AliasUtils.getAliasName(aliasNum.getAndIncrement()), curDqlParseResult.getFrom());
            if (result == null) {
                result = curDqlParseResult;
                if (result.getWhere().size() > 1) {
                    String where = String.join(relationSplit, result.getWhere());
                    where = String.format("(%s)", where);
                    result.getWhere().clear();
                    result.getWhere().add(where);
                }
                if (result.getHaving().size() > 1) {
                    String having = String.join(curDqlParseResult.getRelationSplit(), result.getHaving());
                    having = String.format("(%s)", having);
                    result.getHaving().clear();
                    result.getHaving().add(having);
                }
            } else {
                // 组装where条件
                String addWhere = String.format("%s IN (%s)", result.getSelect(), curDqlParseResult.parseDorisSql());
                result.getWhere().add(addWhere);
                result.addFieldSchema(curDqlParseResult);
            }
        }
        result.setRelation(ruleGroup.getRelation());

        return result;
    }

    @Override
    public DqlParseResult parseRuleNode(RuleNode ruleNode) {
        DqlParseResult dqlParseResult = new DqlParseResult();

        // 组装关联语句
        String fromTable = getFromTable(ruleNode);
        dqlParseResult.setFrom(fromTable);

        // 组装过滤语句
        ruleNode.getFilters().stream()
                .filter(e -> StringUtils.isNotBlank(e.getFiled()))
                .forEach(filter -> {
                            if (Objects.nonNull(filter.getAggregator()) && CollectionUtils.isEmpty(dqlParseResult.getGroupBy())) {
                                String groupByField = getGroupByField(fromTable);
                                dqlParseResult.getGroupBy().add(groupByField);
                            }
                            parseFilter(ruleNode, filter, dqlParseResult);
                        }
                );
        Map<String, String> fieldSchema = dorisService.getFieldSchema(fromTable);
        for (Map.Entry<String, String> entry : fieldSchema.entrySet()) {
            dqlParseResult.addFieldSchema(entry.getKey(), entry.getValue());
        }
        return dqlParseResult;
    }

    @Override
    public void checkRuleNode(RuleNode ruleNode) {
        if (Objects.isNull(ruleNode)
                || Objects.isNull(ruleNode.getType())) {
            throw new DeepSightException.ParamsErrorException(
                    ErrorCode.INTERNAL_ERROR, "rule parse failed, rule is null");
        }
        if (CollectionUtils.isEmpty(ruleNode.getFilters())) {
            log.info("check rule, filter is empty");
            // filter为空时设置默认，避免空指针
            ruleNode.setFilters(List.of());
            return;
        }
        List<Long> fieldIds = ruleNode.getFilters().stream()
                .map(RuleFilter::getFieldId).toList();

        switch (ruleNode.getType()) {
            case LABEL -> checkLabelRuleNode(ruleNode, fieldIds);
            case USER -> checkUserRuleNode(ruleNode, fieldIds);
            case DATASET -> checkDatasetRuleNode(ruleNode, fieldIds);
        }
        // 过滤空、非空查询外的方法，必须有过滤参数
        for (RuleFilter filter : ruleNode.getFilters()) {
            FuncEnum function = filter.getFunction();
            if (!Objects.equals(FuncEnum.IS_NULL, function)
                    && !Objects.equals(FuncEnum.IS_NOT_NULL, function)
                    && CollUtil.isEmpty(filter.getParams())) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "过滤参数为空");
            }
        }
    }

    @Override
    public void checkRuleGroupTenantPermission(RuleGroup ruleGroup, String tenantId) {
        // 如果存在规则组进行检查
        if (CollectionUtils.isNotEmpty(ruleGroup.getRuleGroups())) {
            for (RuleGroup group : ruleGroup.getRuleGroups()) {
                checkRuleGroupTenantPermission(group, tenantId);
            }
        }

        // 如果存在规则节点进行检查
        if (CollectionUtils.isNotEmpty(ruleGroup.getRuleNodes())) {
            for (RuleNode ruleNode : ruleGroup.getRuleNodes()) {
                List<Long> fieldIds = ruleNode.getFilters().stream()
                        .map(RuleFilter::getFieldId).filter(Objects::nonNull).toList();
                switch (ruleNode.getType()) {
                    case LABEL -> {
                        List<Long> labelFieldIds = ruleNode.getFilters().stream()
                                .map(RuleFilter::getFieldId).filter(Objects::nonNull).toList();
                        Map<Long, LabelField> labelFieldMap = getLabelFieldMap(labelFieldIds);
                        for (Map.Entry<Long, LabelField> labelFieldEntry : labelFieldMap.entrySet()) {
                            LabelField labelField = labelFieldEntry.getValue();
                            if (StringUtils.isEmpty(labelField.getLabelTable()) || !labelField.getLabelTable().contains(tenantId)) {
                                log.error("标签未找到或者不属于当前租户：labelFieldId:{}, tenantId:{}", labelFieldEntry.getKey(), tenantId);
                                throw new DeepSightException.ParamsErrorException(
                                        ErrorCode.BAD_REQUEST, "规则解析失败，存在标签未找到或不属于当前租户");
                            }
                        }
                    }
                    case USER, DATASET -> {
                        Map<Long, TableFieldMetaInfo> filedMetaInfoMap = getFiledMetaInfoMap(fieldIds);
                        for (Map.Entry<Long, TableFieldMetaInfo> tableFieldMetaInfoEntry : filedMetaInfoMap.entrySet()) {
                            TableFieldMetaInfo fieldMetaInfo = tableFieldMetaInfoEntry.getValue();
                            if (StringUtils.isEmpty(fieldMetaInfo.getTableEnName()) || !fieldMetaInfo.getTableEnName().contains(tenantId)) {
                                log.error("表未找到或者不属于当前租户：fieldId:{}, tenantId:{}", tableFieldMetaInfoEntry.getKey(), tenantId);
                                throw new DeepSightException.ParamsErrorException(
                                        ErrorCode.BAD_REQUEST, "规则解析失败，存在表未找到或不属于当前租户");
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 校验标签规则节点
     */
    private void checkLabelRuleNode(RuleNode ruleNode, List<Long> fieldIds) {
        Map<Long, LabelField> labelFieldMap = getLabelFieldMap(fieldIds);
        for (RuleFilter filter : ruleNode.getFilters()) {
            Long fieldId = filter.getFieldId();
            LabelField labelField = labelFieldMap.get(fieldId);
            String fieldName = String.format(Constants.DORIS_LABEL_FIELD_TEM, labelField.getId());
            String field = String.format(Constants.DORIS_TABLE_FIELD_TEM,
                    dorisConfiguration.getDb(),
                    labelField.getLabelTable(),
                    fieldName);
            filter.setFiled(field);
            filter.setFieldDataType(labelField.getFieldType());
            ruleNode.setDorisTableName(labelField.getLabelTable());
            checkoutRuleFilter(filter);
        }
    }

    /**
     * 校验数据集规则节点
     */
    public void checkDatasetRuleNode(RuleNode ruleNode, List<Long> fieldIds) {
        Map<Long, TableFieldMetaInfo> filedMetaInfoMap = getFiledMetaInfoMap(fieldIds);
        if (MapUtils.isEmpty(filedMetaInfoMap)) {
            throw new DeepSightException.CustomerGroupFailedException(
                    ErrorCode.NOT_FOUND, "数据表字段不存在");
        }
        for (RuleFilter filter : ruleNode.getFilters()) {
            Long fieldId = filter.getFieldId();
            TableFieldMetaInfo fieldMetaInfo = filedMetaInfoMap.get(fieldId);
            if (fieldMetaInfo == null) {
                continue;
            }
            String field = String.format(
                    Constants.DORIS_TABLE_FIELD_TEM,
                    dorisConfiguration.getDb(),
                    fieldMetaInfo.getTableEnName(),
                    fieldMetaInfo.getEnField());
            filter.setFiled(field);
            filter.setFieldDataType(fieldMetaInfo.getDataType());
            ruleNode.setDataTableId(fieldMetaInfo.getDataTableId());
            ruleNode.setDorisTableName(fieldMetaInfo.getTableEnName());
            checkoutRuleFilter(filter);
        }
    }

    /**
     * 校验用户规则节点
     */
    private void checkUserRuleNode(RuleNode ruleNode, List<Long> fieldIds) {
        checkDatasetRuleNode(ruleNode, fieldIds);
    }

    /**
     * 获取该表 group by 的字段
     */
    private String getGroupByField(String tableName) {
        return String.format(Constants.DORIS_FIELD_TEM, tableName, Constants.TABLE_USER_ONE_ID);
    }

    /**
     * 获取 from 表名
     */
    private String getFromTable(RuleNode ruleNode) {
        if (StringUtils.isEmpty(ruleNode.getDorisTableName()) && Objects.nonNull(ruleNode.getDataTableId())) {
            DataTableInfo dataTableInfo = dataTableInfoMapper.selectByPrimaryKey(ruleNode.getDataTableId());
            ruleNode.setDorisTableName(dataTableInfo.getTableName());
        } else if (StringUtils.isEmpty(ruleNode.getDorisTableName()) && Objects.isNull(ruleNode.getDataTableId())) {
            throw new DeepSightException.ParamsErrorException(
                    ErrorCode.INTERNAL_ERROR, "rule parse failed, 不能获取表名");
        }
        return String.format(Constants.DORIS_TABLE_TEM, dorisConfiguration.getDb(), ruleNode.getDorisTableName());
    }

    /**
     * 获取标签字段 - id 到 LabelField 的映射
     *
     * @param fieldIds 标签字段id列表
     * @return
     */
    private Map<Long, LabelField> getLabelFieldMap(List<Long> fieldIds) {
        if (CollectionUtils.isEmpty(fieldIds)) {
            return Collections.emptyMap();
        }

        LabelFieldCriteria labelFieldCriteria = new LabelFieldCriteria();
        LabelFieldCriteria.Criteria criteria = labelFieldCriteria.createCriteria();
        criteria.andIdIn(fieldIds);
        List<LabelField> labelFields = labelFieldMapper.selectByExample(labelFieldCriteria);
        return labelFields.stream()
                .collect(Collectors.toMap(LabelField::getId, labelField -> labelField));
    }

    /**
     * 获取数据集字段 - id 到 TableFieldMetaInfo 的映射
     *
     * @param fieldIds 数据集字段id列表
     * @return
     */
    public Map<Long, TableFieldMetaInfo> getFiledMetaInfoMap(List<Long> fieldIds) {
        if (CollectionUtils.isEmpty(fieldIds)) {
            return Collections.emptyMap();
        }

        TableFieldMetaInfoCriteria fieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = fieldMetaInfoCriteria.createCriteria();
        criteria.andIdIn(fieldIds);
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(fieldMetaInfoCriteria);
        String tenantId = WebContextHolder.getTenantId();
        return tableFieldMetaInfos.stream()
                .filter(e -> StringUtils.contains(e.getTableEnName(), tenantId))
                .collect(Collectors.toMap(TableFieldMetaInfo::getId, tableFieldMetaInfo -> tableFieldMetaInfo));
    }

    /**
     * 和
     *
     * @param ruleFilter
     * @param dqlParseResult
     */
    private void parseFilter(RuleNode ruleNode, RuleFilter ruleFilter, DqlParseResult dqlParseResult) {
        // 获取kie session , 此处获取的是有状态的session
        KieSession kieSession = KieUtil.getKieSession();
        // 将对象加入到工作内存中
        kieSession.insert(ruleFilter);
        kieSession.insert(ruleNode);
        kieSession.insert(dqlParseResult);
        // 触发所有的规则，如果只想触发指定的规则，则使用fireAllRules(AgendaFilter agendaFilter)方法
        kieSession.fireAllRules();

        // 调用dispose方法释放内存
        kieSession.dispose();
    }

    /**
     * 校验规则节点，防止注入攻击
     */
    private void checkoutRuleFilter(RuleFilter filter) {
        List<String> params = filter.getParams();
        List<String> handledParams = params;
        if (CollectionUtils.isEmpty(params)) {
            log.debug("params is empty");
            return;
        }
        switch (filter.getType()) {
            // 字符串类型添加''
            case STRING -> {
                // 聚类操作的入参是数值
                if (Objects.nonNull(filter.getAggregator())) {
                    handledParams = handleNonStringParam(params);
                    break;
                }
                handledParams = handleStringParam(params);
            }
            // 时间类型 对于 yyyy-MM-dd HH:mm:ss 格式和字符串一致
            case TIME -> {
                if (filter.getFunction() == FuncEnum.LEAST) {
                    handledParams = handleNonStringParam(params);
                    break;
                }
                handledParams = handleStringParam(params);
            }
            case NUMBER -> {
                try {
                    for (String handledParam : handledParams) {
                        Double.parseDouble(handledParam);
                    }
                } catch (Exception e) {
                    throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "rule parse failed, 参数不是数字类型");
                }
            }
            case BOOLEAN -> {
                try {
                    for (String handledParam : handledParams) {
                        Boolean.parseBoolean(handledParam);
                    }
                } catch (Exception e) {
                    throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "rule parse failed, 参数不是布尔类型");
                }
            }
            default -> {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "rule parse failed, 参数类型不支持");
            }
        }
        filter.setParams(handledParams);
        filter.setFiled(StringEscapeUtils.escapeSql(filter.getFiled()));
    }

    /**
     * 字符串类型参数处理
     */
    private List<String> handleStringParam(List<String> params) {
        return params.stream()
                .map(this::escapeForQuery)
                .map(param -> String.format("'%s'", param))
                .toList();
    }

    /**
     * 非字符串类型参数处理
     */
    private List<String> handleNonStringParam(List<String> params) {
        return params.stream()
                .map(StringEscapeUtils::escapeSql)
                .toList();
    }

    /**
     * 用于防止 SQL 注入的字符串转义函数，适用于 SELECT 查询条件。
     * 不适用于 LIKE 模式匹配。
     *
     * @param input 用户输入
     * @return 转义后的字符串
     */
    private String escapeForQuery(String input) {
        if (input == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder(input.length() * 2);
        for (char c : input.toCharArray()) {
            switch (c) {
                case 0:     // NULL 字符
                    sb.append("\\0");
                    break;
                case '\n':  // 换行符
                    sb.append("\\n");
                    break;
                case '\r':  // 回车符
                    sb.append("\\r");
                    break;
                case '\\':  // 反斜杠
                    sb.append("\\\\");
                    break;
                case '\'':  // 单引号
                    sb.append("\\'");
                    break;
                case '\"':  // 双引号
                    sb.append("\\\"");
                    break;
                case '\032': // Ctrl-Z
                    sb.append("\\Z");
                    break;
                default:
                    sb.append(c);
            }
        }
        return sb.toString();
    }

}
