package com.baidu.keyue.deepsight.service.customer.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.sax.handler.RowHandler;
import com.baidu.keyue.deepsight.config.BosConfig;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.CustomerCalculateConfiguration;
import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.GroupingTypeEnum;
import com.baidu.keyue.deepsight.enums.LogicEnum;
import com.baidu.keyue.deepsight.enums.MatchPoliciesEnum;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.customer.request.CreateCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.CreateCustomerImportRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerGroupDetaiListlRequest;
import com.baidu.keyue.deepsight.models.customer.request.DeleteCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.DeleteCustomerRequest;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerListRequest;
import com.baidu.keyue.deepsight.models.customer.request.ListCustomerGroupAnalysisRequest;
import com.baidu.keyue.deepsight.models.customer.request.SamplingStatisticsCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.UpdateCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.response.CreateCustomerImportResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDorisResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerGroupAnalysisItemResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerGroupDetailResponse;
import com.baidu.keyue.deepsight.models.customer.response.GroupDetailDto;
import com.baidu.keyue.deepsight.models.customer.response.SamplingStatisticsCustomerGroupResponse;
import com.baidu.keyue.deepsight.models.datamanage.dto.FileDetailDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableRecordMsgDTO;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroupCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.CustomerGroupMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.DorisConfServiceImpl;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.service.tool.BosUtils;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidubce.services.bos.model.BosObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * @ClassName CustomerGroupServiceImpl
 * @Description 客群分析数据操作实现
 * <AUTHOR> Chen (<EMAIL>)
 * @Date 2025/1/13 11:30
 */
@Slf4j
@Service
public class CustomerGroupServiceImpl implements CustomerGroupService {

    @Autowired
    @Qualifier("customerGroupExecutor")
    private ExecutorService executorService;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private CustomerGroupMapper customerGroupMapper;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private TaskSchedulerService taskSchedulerService;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private RuleManagerService ruleManagerService;

    @Autowired
    private RuleParseService ruleParseService;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private DorisConfiguration dorisConfiguration;

    @Autowired
    private DorisConfServiceImpl dorisConfService;

    @Autowired
    private CustomerCalculateConfiguration customerCalculateConfiguration;
    @Resource
    private BosUtils bosUtils;

    @Resource
    private BosConfig bosConfig;

    @Resource
    private DataTableManageService dataTableManageService;

    @Resource
    private TableRecordCommonService tableRecordCommonService;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Value("${file.upload.suffixNames}")
    private String suffixNames;

    @Value("${kafka.topics.dataSync:deep_sight_data_sync}")
    private String topic;


    @Value("${customer-group.import-ignore-match-field}")
    private String ignoreMatchFields;

    /**
     * 导入客群匹配时忽略字段
     */
    private final Set<String> ignoreField = new HashSet<>();

    public String customerGroupLock(String... keys) {
        return StringUtils.join(keys, "-");
    }

    @PostConstruct
    public void initService() {
        if (StrUtil.isNotBlank(ignoreMatchFields)) {
            ignoreField.addAll(List.of(ignoreMatchFields.split(",")));
        }
    }

    @Override
    public Long createCustomerGroup(CreateCustomerGroupRequest request) {
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        ruleParseService.checkRuleGroupTenantPermission(request.getCustomerGroupRule(), tenantId);
        String key = customerGroupLock("createCustomerGroup",
                userId,
                request.getCustomerGroupName());
        RLock lock = redisson.getLock(key);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            // 检查标签名称是否重复
            CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
            CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
            criteria.andTenantIdEqualTo(tenantId)
                    .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                    .andCustomerGroupNameEqualTo(request.getCustomerGroupName());
            long count = customerGroupMapper.countByExample(customerGroupCriteria);
            if (count > 0) {
                throw new DeepSightException.CustomerGroupFailedException(ErrorCode.BAD_REQUEST, "客群已存在");
            }

            // insert customerGroup
            CustomerGroup customerGroup = new CustomerGroup();
            customerGroup.setUserId(userId);
            customerGroup.setTenantId(tenantId);
            customerGroup.setCustomerGroupName(request.getCustomerGroupName());
            customerGroup.setCustomerGroupDescription(request.getCustomerGroupDescription());
            customerGroup.setCustomerGroupValueUpdateMod(request.getUpdateMod().getCode());
            customerGroup.setTriggerMod(request.getTriggerMod().getCode());
            if (Objects.nonNull(request.getTriggerFrequency())) {
                customerGroup.setTriggerFrequency(request.getTriggerFrequency().getCode());
            }
            if (Objects.nonNull(request.getTriggerFrequencyValue())) {
                customerGroup.setTriggerFrequencyValue(JsonUtils.toJson(request.getTriggerFrequencyValue()));
            }
            customerGroup.setCustomerGroupRule(JsonUtils.toJson(request.getCustomerGroupRule()));
            customerGroup.setCalStatus(TaskExecStatusEnum.PENDING.getCode());

            // 客群生产任务创建：导入创建的客群无需计算
            if (Objects.equals(true, request.getNeedCal())) {
                Long taskId = taskInfoService.createCalTask(
                        TaskTypeEnum.CUSTOMER_DWS_TASK,
                        request.getTriggerMod(),
                        request.getTriggerFrequency(),
                        request.getTriggerFrequencyValue());
                customerGroup.setTask(taskId);
            }
            customerGroup.setDel(DelEnum.NOT_DELETED.getBoolean());
            customerGroup.setCreator(WebContextHolder.getUserAuthInfo().getUserName());
            customerGroup.setModifier(WebContextHolder.getUserAuthInfo().getUserName());
            Date now = new Date();
            customerGroup.setCreateTime(now);
            customerGroup.setUpdateTime(now);
            // 补充分群方式、配置操作标签（默认可操作）
            GroupingTypeEnum groupingType = request.getGroupingType();
            Boolean configTag = request.getConfigTag();
            customerGroup.setGroupingType(groupingType != null ? groupingType.getCode() : GroupingTypeEnum.RULE_CIRCLE.getCode());
            customerGroup.setConfigTag(configTag != null ? configTag : true);
            customerGroupMapper.insert(customerGroup);
            // 创建客群宽表字段
            newDorisField(tenantId, genFieldName(customerGroup.getId()));
            return customerGroup.getId();
        } catch (DeepSightException.CustomerGroupFailedException e) {
            log.error("CustomerGRoupService.createCustomerGroup error : ", e);
            throw new DeepSightException.CustomerGroupFailedException(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("CustomerGRoupService.createCustomerGroup INTERNAL_ERROR : ", e);
            throw new DeepSightException.CustomerGroupFailedException(ErrorCode.INTERNAL_ERROR, "客群创建失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 创建固定前缀的客群字段
     *
     * @param fieldId
     * @return
     */
    private String genFieldName(long fieldId) {
        return StringEscapeUtils.escapeSql(String.format("process_customer_%d", fieldId));
    }

    /**
     * 创建用户宽表doris新字段
     *
     * @param fieldName
     */
    private void newDorisField(String tenantId, String fieldName) {
        try {
            dorisService.execSql(String.format(
                    "ALTER TABLE %s ADD COLUMN %s varchar(150) NULL DEFAULT '0'",
                    TenantUtils.generateMockUserTableName(tenantId), fieldName));
            dorisService.execSql(String.format(
                    "ALTER TABLE %s ADD COLUMN %s varchar(150) NULL DEFAULT '0'",
                    TenantUtils.generateUserProfileTableName(tenantId), fieldName));
        } catch (Exception e) {
            log.error("CustomerGroupServiceImpl.newDorisField error : ", e);
        }
    }

    private void newDefaultDorisField(String tenantId, String fieldName) {
        try {
            dorisService.execSql(String.format(
                    "ALTER TABLE %s ADD COLUMN %s varchar(150) NULL DEFAULT '1'",
                    TenantUtils.generateMockUserTableName(tenantId), fieldName));
            dorisService.execSql(String.format(
                    "ALTER TABLE %s ADD COLUMN %s varchar(150) NULL DEFAULT '1'",
                    TenantUtils.generateUserProfileTableName(tenantId), fieldName));
        } catch (Exception e) {
            log.error("CustomerGroupServiceImpl.newDefaultDorisField error : ", e);
        }
    }

    @Override
    public BasePageResponse.Page<CustomerGroupAnalysisItemResponse> customerGroupAnalysisItems(ListCustomerGroupAnalysisRequest request) {
        // 客群列表查询
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        DataTableInfo dataTableInfo = getCustomerTableByTenantId(tenantId);

        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andTenantIdEqualTo(tenantId);

        if (StringUtils.isNotBlank(request.getCustomerGroupName())) {
            criteria.andCustomerGroupNameLike("%" + request.getCustomerGroupName() + "%");
        }
        if (CollectionUtils.isNotEmpty(request.getCustomerGroupIds())) {
            criteria.andIdIn(request.getCustomerGroupIds());
        }

        long count = customerGroupMapper.countByExample(customerGroupCriteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }

        // 翻页组件
        PageHelper.startPage(request.getPageNo(), request.getPageSize());

        customerGroupCriteria.setOrderByClause(Constants.ORDER_BY_PRESET_WITH_CREATE_TIME_DESC);

        // 分页查询
        List<CustomerGroup> customerGroups = customerGroupMapper.selectByExample(customerGroupCriteria);

        // 查询 task 对应的最新执行记录
        List<Long> taskIds = customerGroups.stream().map(CustomerGroup::getTask).distinct().toList();

        Map<Long, TaskSchedulerWithBLOBs> taskSchedulerRecordMap = taskSchedulerService.queryTaskScheduler(taskIds);

        List<CustomerGroupAnalysisItemResponse> customerGroupAnalysisItemResponseList = customerGroups.stream().map(customerGroup -> {
            CustomerGroupAnalysisItemResponse customerGroupAnalysisItemResponse = new CustomerGroupAnalysisItemResponse();
            customerGroupAnalysisItemResponse.setCustomerGroupId(customerGroup.getId());
            customerGroupAnalysisItemResponse.setDataTableId(dataTableInfo.getId());
            customerGroupAnalysisItemResponse.setCustomerGroupName(customerGroup.getCustomerGroupName());
            TaskExecStatusEnum statusEnum = TaskExecStatusEnum.getByCode(customerGroup.getCalStatus());
            customerGroupAnalysisItemResponse.setStatus(statusEnum);
            if (TaskExecStatusEnum.FAILED.equals(statusEnum)) {
                Long taskId = customerGroup.getTask();
                TaskSchedulerWithBLOBs schedulerRecord = taskSchedulerRecordMap.get(taskId);
                if (Objects.nonNull(schedulerRecord)) {
                    customerGroupAnalysisItemResponse.setErrorMessage(schedulerRecord.getMessage());
                }
            } else {
                customerGroupAnalysisItemResponse.setErrorMessage("");
            }
            customerGroupAnalysisItemResponse.setCreator(customerGroup.getCreator());
            customerGroupAnalysisItemResponse.setCreateTime(DatetimeUtils.formatDate(customerGroup.getCreateTime()));
            customerGroupAnalysisItemResponse.setIsPreset(customerGroup.getPreset());
            customerGroupAnalysisItemResponse.setGroupingType(GroupingTypeEnum.getByCode(customerGroup.getGroupingType()));
            customerGroupAnalysisItemResponse.setConfigTag(customerGroup.getConfigTag());
            return customerGroupAnalysisItemResponse;
        }).toList();

        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, customerGroupAnalysisItemResponseList);
    }

    @Override
    public void deleteCustomerGroup(DeleteCustomerGroupRequest request) {
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        CustomerGroup customerGroup = getCustomerGroupByTenantIdAndCustomerGroupId(tenantId, request.getCustomerGroupId());
        if (Objects.isNull(customerGroup)) {
            throw new DeepSightException.CustomerGroupFailedException(ErrorCode.NOT_FOUND, "客群不存在");
        }
        customerGroup.setDel(DelEnum.DELETED.getBoolean());
        Date now = new Date();
        customerGroup.setLastCalDate(now);
        customerGroup.setModifier(userId);
        customerGroup.setUpdateTime(now);
        customerGroupMapper.updateByPrimaryKey(customerGroup);
        // 删除任务
        if (Objects.nonNull(customerGroup.getTask())) {
            taskInfoService.deleteFieldById(customerGroup.getTask());
        }
    }

    @Override
    public CustomerGroupDetailResponse getCustomerGroupDetail(GetCustomerGroupRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        DataTableInfo dataTableInfo = getCustomerTableByTenantId(tenantId);

        CustomerGroup customerGroup = getCustomerGroupByTenantIdAndCustomerGroupId(tenantId, request.getCustomerGroupId());

        CustomerGroupDetailResponse response = new CustomerGroupDetailResponse();
        response.setCustomerGroupName(customerGroup.getCustomerGroupName());
        response.setCustomerGroupId(customerGroup.getId());
        response.setDataTableId(dataTableInfo.getId());
        response.setCustomerGroupDescription(customerGroup.getCustomerGroupDescription());
        response.setCreateTime(DatetimeUtils.formatDate(customerGroup.getCreateTime()));
        response.setCreator(customerGroup.getCreator());
        response.setStatus(TaskExecStatusEnum.getByCode(customerGroup.getCalStatus()));

        // 更新方式&更新取值逻辑
        response.setTriggerMod(TriggerModeEnum.getByCode(customerGroup.getTriggerMod()));
        response.setTriggerFrequency(TriggerFrequencyEnum.getByCode(customerGroup.getTriggerFrequency()));
        if (StringUtils.isNotBlank(customerGroup.getTriggerFrequencyValue())) {
            response.setTriggerFrequencyValue(JsonUtils.toObjectWithoutException(customerGroup.getTriggerFrequencyValue(), TriggerFrequencyValue.class));
        }
        response.setUpdateMod(UpdateModEnum.getByCode(customerGroup.getCustomerGroupValueUpdateMod()));

        // 客群圈选规则
        if (StringUtils.isNotBlank(customerGroup.getCustomerGroupRule())) {
            response.setCustomerGroupRule(JsonUtils.toObjectWithoutException(customerGroup.getCustomerGroupRule(), RuleGroup.class));
        }
        response.setIsPreset(customerGroup.getPreset());

        response.setLastTaskDate(DatetimeUtils.formatDate(customerGroup.getLastCalDate()));
        response.setGroupingType(GroupingTypeEnum.getByCode(customerGroup.getGroupingType()));
        response.setConfigTag(customerGroup.getConfigTag());
        return response;

    }


    @Override
    public List<CustomerGroupDetailResponse> listCustomerGroupDetail(CustomerGroupDetaiListlRequest request) {
        if (Objects.isNull(request) || CollectionUtils.isEmpty(request.getCustomerGroupIds())) {
            return Lists.newArrayList();
        }
        // 去重
        List<Long> customerGroupIds = request.getCustomerGroupIds().stream()
                .distinct().collect(Collectors.toList());
        List<CustomerGroupDetailResponse> detailList = new ArrayList<>();
        for (Long customerGroupId : customerGroupIds) {
            CustomerGroupDetailResponse detail = getCustomerGroupDetail(new GetCustomerGroupRequest(customerGroupId));
            if (Objects.nonNull(detail)) {
                detailList.add(detail);
            }
        }
        return detailList;
    }

    @Override
    public SamplingStatisticsCustomerGroupResponse samplingStatisticsConsumerGroup(SamplingStatisticsCustomerGroupRequest request) {
        SamplingStatisticsCustomerGroupResponse countResponse = new SamplingStatisticsCustomerGroupResponse();
        CustomerGroup customerGroup = customerGroupMapper.selectByPrimaryKey(request.getCustomerGroupId());
        if (Objects.isNull(customerGroup)) {
            log.info("samplingStatisticsConsumerGroup customerGroup is null");
            return countResponse;
        }
        TaskExecStatusEnum execStatus = TaskExecStatusEnum.getByCode(customerGroup.getCalStatus());
        if (Objects.nonNull(execStatus) && TaskExecStatusEnum.SUCCESS.equals(execStatus)) {
            Long userCount = getUserCount(customerGroup.getTenantId(), customerGroup.getId());
            countResponse.setCount(userCount);
            return countResponse;
        }
        if (StringUtils.isEmpty(customerGroup.getCustomerGroupRule())) {
            log.info("samplingStatisticsConsumerGroup customerGroup rule is empty");
            return countResponse;
        }

        // 获取基础用户总数
        String userTableName = TenantUtils.generateMockUserTableName(customerGroup.getTenantId());
        userTableName = String.format(Constants.DORIS_TABLE_TEM, dorisConfiguration.getDb(), userTableName);
        long userCount = dorisService.getSingleTableCount(userTableName);

        // 解析客群规则，获取采样统计客群数
        Integer sampleNumber = customerCalculateConfiguration.getSampleNumber();
        RuleGroup ruleGroup = JsonUtils.toObjectWithoutException(customerGroup.getCustomerGroupRule(), RuleGroup.class);
        DqlParseResult dqlParseResult = ruleManagerService.parseRuleGroup(ruleGroup, new AtomicInteger(0));
        String sampleStatisticsSql = dqlParseResult.parseSampleStatisticsSql(sampleNumber, userTableName);
        long sampleCount = dorisService.getCount(sampleStatisticsSql);

        // 预估客群圈选人数
        if (userCount <= sampleNumber) {
            countResponse.setCount(sampleCount);
        } else {
            long predictCustomerCount = (userCount / sampleNumber) * sampleCount;
            countResponse.setCount(predictCustomerCount);
        }
        return countResponse;
    }

    @Override
    public CustomerGroup getCustomerGroupByTenantIdAndCustomerGroupId(String tenantId, Long customerGroupId) {
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andTenantIdEqualTo(tenantId).andDelEqualTo(DelEnum.NOT_DELETED.getBoolean()).andIdEqualTo(customerGroupId);
        List<CustomerGroup> customerGroups = customerGroupMapper.selectByExampleWithBLOBs(customerGroupCriteria);
        if (CollectionUtils.isEmpty(customerGroups)) {
            throw new DeepSightException.CustomerGroupFailedException(ErrorCode.NOT_FOUND, "客群不存在");
        }
        return customerGroups.get(0);
    }

    @Override
    public CustomerGroup getByIdAndTenantId(Long customerGroupId, String tenantId) {
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andTenantIdEqualTo(tenantId).andDelEqualTo(DelEnum.NOT_DELETED.getBoolean()).andIdEqualTo(customerGroupId);
        List<CustomerGroup> customerGroups = customerGroupMapper.selectByExampleWithBLOBs(customerGroupCriteria);
        return CollUtil.isEmpty(customerGroups) ? null : customerGroups.get(0);
    }

    /**
     * 更新任务状态
     *
     * @param customerGroup
     * @param statusEnum
     */
    @Override
    public void updateCustomerGroupCalTaskStatus(CustomerGroup customerGroup, TaskExecStatusEnum statusEnum) {
        Date now = new Date();
        customerGroup.setUpdateTime(now);
        customerGroup.setCalStatus(statusEnum.getCode());
        customerGroup.setLastCalDate(now);
        customerGroupMapper.updateByPrimaryKeySelective(customerGroup);
    }

    @Override
    public List<CustomerGroup> queryRunningCustomer() {
        CustomerGroupCriteria labelCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = labelCriteria.createCriteria();
        criteria.andCalStatusEqualTo(TaskExecStatusEnum.RUNNING.getCode());
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        return customerGroupMapper.selectByExampleWithBLOBs(labelCriteria);
    }

    @Override
    public List<CustomerGroup> getWaitExecCustomer(Set<Long> taskIds) {
        // retrieve customer group
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andCalStatusNotEqualTo(TaskExecStatusEnum.RUNNING.getCode());
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        criteria.andTaskIn(Lists.newArrayList(taskIds));
        return customerGroupMapper.selectByExampleWithBLOBs(customerGroupCriteria);
    }

    @Override
    public List<CustomerGroup> queryDeletedLabel(Integer seconds) {
        Date d = new Date(System.currentTimeMillis() - (seconds * 1000));
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andDelEqualTo(DelEnum.DELETED.getBoolean())
                .andUpdateTimeGreaterThan(d);
        return customerGroupMapper.selectByExample(customerGroupCriteria);
    }

    @Override
    public List<CustomerGroup> retrieveCustomerGroupWithIds(List<Long> ids, String tenantId) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andTenantIdEqualTo(tenantId)
                .andIdIn(ids);
        return customerGroupMapper.selectByExample(customerGroupCriteria);
    }

    @Override
    public void updateCustomerGroup(UpdateCustomerGroupRequest request) {
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        ruleParseService.checkRuleGroupTenantPermission(request.getCustomerGroupRule(), tenantId);
        String key = customerGroupLock("updateCustomerGroup",
                tenantId,
                request.getCustomerGroupName(),
                String.valueOf(request.getCustomerGroupId()));
        RLock lock = redisson.getLock(key);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        try {
            CustomerGroup customerGroup = getCustomerGroupByTenantIdAndCustomerGroupId(tenantId, request.getCustomerGroupId());
            if (customerGroup.getCalStatus().equals(TaskExecStatusEnum.RUNNING.getCode())) {
                throw new DeepSightException.CustomerGroupFailedException(ErrorCode.BUSY_REQUEST, "客群正在计算中，请先停止计算");
            }
            customerGroup.setCustomerGroupName(request.getCustomerGroupName());
            customerGroup.setCustomerGroupDescription(request.getCustomerGroupDescription());
            customerGroup.setCustomerGroupValueUpdateMod(request.getUpdateMod().getCode());
            customerGroup.setTriggerMod(request.getTriggerMod().getCode());
            if (Objects.nonNull(request.getTriggerFrequency())) {
                customerGroup.setTriggerFrequency(request.getTriggerFrequency().getCode());
            }
            if (Objects.nonNull(request.getTriggerFrequencyValue())) {
                customerGroup.setTriggerFrequencyValue(JsonUtils.toJson(request.getTriggerFrequencyValue()));
            }
            customerGroup.setCustomerGroupRule(JsonUtils.toJson(request.getCustomerGroupRule()));

            customerGroup.setModifier(userId);
            Date now = new Date();
            customerGroup.setUpdateTime(now);
            customerGroup.setCalStatus(TaskExecStatusEnum.PENDING.getCode());
            customerGroupMapper.updateByPrimaryKeySelective(customerGroup);

            // task更新
            taskInfoService.updateLabelCalTaskTrigger(
                    customerGroup.getTask(),
                    request.getTriggerMod(),
                    request.getTriggerFrequency(),
                    request.getTriggerFrequencyValue());

        } catch (DeepSightException.CustomerGroupFailedException e) {
            log.error("CustomerGroupService.updateCustomerGroup error : ", e);
            throw new DeepSightException.CustomerGroupFailedException(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("CustomerGroupService.updateCustomerGroup error : ", e);
            throw new DeepSightException.CustomerGroupFailedException(ErrorCode.INTERNAL_ERROR, "客群更新失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 获取可展示字段
     *
     * @param dataTableId
     * @return
     */
    private List<VisibleFieldResponse> getVisibleFields(Long dataTableId) {
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = tableFieldMetaInfoCriteria.createCriteria();
        criteria.andDataTableIdEqualTo(dataTableId);
        criteria.andIsVisableEqualTo(true);
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper
                .selectByExampleWithBLOBs(tableFieldMetaInfoCriteria);

        return tableFieldMetaInfos.stream().map(VisibleFieldResponse::convertFrom).toList();
    }

    /**
     * 查询表对应类型的字段
     */
    private TableFieldMetaInfo queryTableFieldByTag(Long dataTableId, TableFieldTagEnum tag) {
        TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
        criteria.createCriteria().andDataTableIdEqualTo(dataTableId).andFieldTagEqualTo(tag.getCode());
        List<TableFieldMetaInfo> info = tableFieldMetaInfoMapper.selectByExampleWithBLOBs(criteria);
        return CollectionUtils.isEmpty(info) ? null : info.get(0);
    }

    @Override
    public CustomerDorisResponse getCustomerListDoriSql(GetCustomerListRequest request) {
        CustomerDorisResponse res = new CustomerDorisResponse();
        String logId = WebContextHolder.getRequestId();
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        DataTableInfo dataTableInfo = getCustomerTableByTenantId(tenantId);
        Long dataTableId = dataTableInfo.getId();
        res.setDataTableId(dataTableId);
        // 获取可见字段
        List<VisibleFieldResponse> visibleFieldResponse = getVisibleFields(dataTableId);
        if (CollectionUtils.isEmpty(visibleFieldResponse)) {
            throw new DeepSightException.CustomerGroupFailedException(
                    ErrorCode.NOT_FOUND, "数据表不存在可见字段");
        }
        DqlParseResult dqlParseResult = getCustomerCountDoriSql(tenantId, request, visibleFieldResponse);
        res.setDorisTableName(TenantUtils.generateMockUserTableName(tenantId));
        log.info("getCustomerList: {},  sql: {}", logId, dqlParseResult.parseCountSql());
        res.setCountSql(dqlParseResult.parseCountSql());
        // 获取数据总量
        long count = dorisService.getCount(dqlParseResult.parseCountSql());
        res.setCount(count);
        dqlParseResult = getCustomerSelectDoriSql(dqlParseResult, request, dataTableId);
        res.setSelectSql(dqlParseResult.parseDorisSql());
        return res;
    }


    private DqlParseResult getCustomerCountDoriSql(String tenantId, GetCustomerListRequest request
            , List<VisibleFieldResponse> visibleFieldResponse) {
        // 获取可见字段
        if (CollectionUtils.isEmpty(visibleFieldResponse)) {
            throw new DeepSightException.CustomerGroupFailedException(
                    ErrorCode.NOT_FOUND, "数据表不存在可见字段");
        }
        // 获取doris表名，查询获取内容
        // 构造 ruleNode
        RuleNode ruleNode = RuleNode.builder()
                .dorisTableName(TenantUtils.generateMockUserTableName(tenantId))
                .filters(request.getFilters())
                .type(RuleTypeEnum.DATASET).build();
        // 解析规则节点，生成doris查询语句，设置查询字段
        DqlParseResult dqlParseResult = ruleManagerService.parseRuleNode(ruleNode);
        List<String> visibleFields = visibleFieldResponse.stream()
                .map(field -> String.format("`%s`", StringEscapeUtils.escapeSql(field.getEnName()))).toList();
        dqlParseResult.setSelect(String.join(Constants.SEPARATOR, visibleFields));
        dqlParseResult.getWhere().add(String.format("%s = %s", genFieldName(request.getCustomerGroupId()), "1"));
        return dqlParseResult;
    }

    private DqlParseResult getCustomerSelectDoriSql(DqlParseResult dqlParseResult
            , GetCustomerListRequest request, Long dataTableId) {
        String primaryKey = Objects.requireNonNull(queryTableFieldByTag(dataTableId, TableFieldTagEnum.PRIMARY)).getEnField();
        dqlParseResult.setOrderBy(String.format("`%s`", primaryKey));
        dqlParseResult.setOffset((request.getPageNo() - 1) * request.getPageSize());
        dqlParseResult.setSize(request.getPageSize());
        return dqlParseResult;
    }

    @Override
    public BasePageResponse.Page<Map<String, String>> getCustomerList(GetCustomerListRequest request) {
        String logId = WebContextHolder.getRequestId();
        // 获取doris表名，查询获取内容
        // 构造 ruleNode
        String tenantId = WebContextHolder.getTenantId();
        DataTableInfo dataTableInfo = getCustomerTableByTenantId(tenantId);
        Long dataTableId = dataTableInfo.getId();
        // 获取可见字段
        List<VisibleFieldResponse> visibleFieldResponse = getVisibleFields(dataTableId);
        if (CollectionUtils.isEmpty(visibleFieldResponse)) {
            throw new DeepSightException.CustomerGroupFailedException(
                    ErrorCode.NOT_FOUND, "数据表不存在可见字段");
        }
        DqlParseResult dqlParseResult = getCustomerCountDoriSql(tenantId, request, visibleFieldResponse);
        log.info("getCustomerList: {},  sql: {}", logId, dqlParseResult.parseCountSql());
        // 获取数据总量
        long count = dorisService.getCount(dqlParseResult.parseCountSql());
        getCustomerSelectDoriSql(dqlParseResult, request, dataTableId);
        List<Map<String, Object>> list = dorisService.selectList(dqlParseResult.parseDorisSql());
        List<Map<String, String>> results = list.stream().map(dorisData ->
                dorisConfService.dorisDataConvertToShowData(dataTableId, dorisData, visibleFieldResponse)).toList();

        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, results);
    }

    /**
     * 根据租户ID获取用户表信息
     */
    private DataTableInfo getCustomerTableByTenantId(String tenantId) {
        DataTableInfoCriteria criteria = new DataTableInfoCriteria();
        criteria.createCriteria().andTableNameEqualTo(TenantUtils.generateMockUserTableName(tenantId));
        return dataTableInfoMapper.selectByExample(criteria).stream().findFirst().orElse(null);
    }

    @Override
    public void deleteCustomer(DeleteCustomerRequest request) {
        // 验证表
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        DataTableInfo tableInfo = getCustomerTableByTenantId(tenantId);
        if (null == tableInfo) {
            throw new DeepSightException.CustomerGroupFailedException(ErrorCode.INTERNAL_ERROR, "不存在的表");
        }
        Long dataTableId = tableInfo.getId();
        // 查询表对应的主键名称
        TableFieldMetaInfo metaInfo = queryTableFieldByTag(dataTableId, TableFieldTagEnum.PRIMARY);
        assert metaInfo != null;
        if (null == metaInfo.getEnField()) {
            throw new DeepSightException.CustomerGroupFailedException(ErrorCode.INTERNAL_ERROR, "请检查主键字段");
        }
        List<String> idList = request.getIds();
        // 将主键ID数组转换为逗号分隔的字符串
        String ids = idList.stream()
                .map(StringEscapeUtils::escapeSql)
                .map(s -> "\"" + s + "\"")
                .collect(Collectors.joining(","));
        if ("DELETE".equals(request.getOp())) {
            String updateSql = String.format("update %s AS a set a.%s = '%s' where %s in (%s)",
                    tableInfo.getTableName(),
                    genFieldName(request.getCustomerGroupId()),
                    "0",
                    metaInfo.getEnField(),
                    ids);
            try {
                dorisService.execSql(updateSql);
            } catch (Exception e) {
                throw new DeepSightException.CustomerGroupFailedException(ErrorCode.INTERNAL_ERROR, "删除用户失败");
            }

        }
    }

    /**
     * 全员客群初始化
     *
     * @param tenantId 租户ID
     * @param userId   用户ID
     */
    @Override
    public void initDefaultConsumerGroup(String tenantId, String userId) {
        // 检查标签名称是否重复
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andCustomerGroupNameEqualTo(Constants.DEFAULT_CUSTOMER_GROUP_FOR_ALL);
        long count = customerGroupMapper.countByExample(customerGroupCriteria);
        if (count > 0) {
            return;
        }

        CustomerGroup customerGroup = new CustomerGroup();
        customerGroup.setUserId(Constants.SYSTEM_DEFAULT_USER_ID);
        customerGroup.setTenantId(tenantId);
        customerGroup.setCustomerGroupName(Constants.DEFAULT_CUSTOMER_GROUP_FOR_ALL);
        customerGroup.setCustomerGroupDescription("");
        customerGroup.setCustomerGroupValueUpdateMod(UpdateModEnum.REPLACE.getCode());
        customerGroup.setTriggerMod(TriggerModeEnum.MANUAL.getCode());
        customerGroup.setCustomerGroupRule("");
        customerGroup.setCalStatus(TaskExecStatusEnum.SUCCESS.getCode());
        customerGroup.setTask(0L);

        customerGroup.setDel(DelEnum.NOT_DELETED.getBoolean());
        customerGroup.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
        customerGroup.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
        Date now = new Date();
        customerGroup.setCreateTime(now);
        customerGroup.setUpdateTime(now);
        // 补充分群方式、是否可配置参数
        customerGroup.setGroupingType(GroupingTypeEnum.RULE_CIRCLE.getCode());
        customerGroup.setConfigTag(false);

        try {
            customerGroupMapper.insert(customerGroup);
            newDefaultDorisField(tenantId, genFieldName(customerGroup.getId()));
        } catch (Exception e) {
            log.error("CustomerGroupService.initDefaultConsumerGroup error : ", e);
        }
    }

    @Override
    public CreateCustomerImportResponse importData(CreateCustomerImportRequest request) {
        CreateCustomerImportResponse response = new CreateCustomerImportResponse();
        String tenantId = WebContextHolder.getTenantId();
        // 客群名校验
        String name = request.getName();
        CustomerGroupCriteria groupCriteria = new CustomerGroupCriteria();
        groupCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(false)
                .andCustomerGroupNameEqualTo(name);
        List<CustomerGroup> customerGroups = customerGroupMapper.selectByExample(groupCriteria);
        if (CollUtil.isNotEmpty(customerGroups)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "客群名称重复");
        }
        // 数据校验
        List<FileDetailDto> files = request.getFiles();
        String dataSync = bosConfig.getBucket().getDataSync();
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
        DataTableInfo tableInfo = tableRecordCommonService.getTableByTableName(mockUserTableName);
        Long tableId = tableInfo.getId();
        // 获取可见字段并排除不可能作为匹配条件的字段
        List<VisibleFieldResponse> visibleFields = dataTableManageService.getVisibleFields(mockUserTableName, false);
        Set<String> visibleNames = new HashSet<>();
        Map<String, String> fileTypeMap = new HashMap<>();
        for (VisibleFieldResponse field : visibleFields) {
            String enName = field.getEnName();
            if (!ignoreField.contains(enName)) {
                visibleNames.add(enName);
            }
            fileTypeMap.put(enName, field.getDataType());
        }
        List<String> enNames = checkData(files, dataSync);
        // 导入数据与可见字段必须有交集
        Collection<String> intersection = CollUtil.intersection(visibleNames, enNames);
        if (CollUtil.isEmpty(intersection)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "无任何字段与客群映射");
        }
        response.setInterNames(new HashSet<>(intersection));
        // 客群创建
        CreateCustomerGroupRequest customerGroupRequest = new CreateCustomerGroupRequest();
        customerGroupRequest.setCustomerGroupName(name);
        customerGroupRequest.setCustomerGroupRule(new RuleGroup());
        customerGroupRequest.setUpdateMod(UpdateModEnum.REPLACE);
        customerGroupRequest.setTriggerMod(TriggerModeEnum.MANUAL);
        customerGroupRequest.setNeedCal(false);
        customerGroupRequest.setGroupingType(GroupingTypeEnum.FILE_IMPORT);
        customerGroupRequest.setConfigTag(false);
        Long customerGroupId = createCustomerGroup(customerGroupRequest);
        response.setCustomerGroupId(customerGroupId);
        // 数据导入
        List<Map<String, Object>> errorDataList = new ArrayList<>();
        MatchPoliciesEnum matchPolicies = request.getMatchPolicies();
        List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>();
        for (FileDetailDto file : files) {
            String bosKey = file.getBosKey();
            // 首行字段校验
            String[] split = bosKey.split("\\.");
            String suffix = split[split.length - 1];
            futures.add(CompletableFuture.supplyAsync(() -> {
                try {
                    if (Objects.equals("csv", suffix)) {
                        return importByCsv(file, response, fileTypeMap, tableId, tenantId, matchPolicies);
                    } else {
                        return importByExcel(file, response, fileTypeMap, tableId, tenantId, matchPolicies);
                    }
                } catch (Exception e) {
                    log.error("导入客群异常，filename：{}, ", file.getFileName(), e);
                    response.getMessage().add(file.getFileName() + "导入客群异常");
                }
                return Collections.emptyList();
            }));
        }
        futures.stream().map(CompletableFuture::join).filter(Objects::nonNull).forEach(errorDataList::addAll);
        response.setErrorList(errorDataList);
        CustomerGroup customerGroup = new CustomerGroup();
        customerGroup.setId(customerGroupId);
        customerGroup.setCalStatus(TaskExecStatusEnum.SUCCESS.getCode());
        customerGroupMapper.updateByPrimaryKeySelective(customerGroup);
        return response;
    }

    /**
     * 导入excel文件客群数据
     *
     * @param file          file
     * @param response      响应
     * @param fileTypeMap   字段类型map
     * @param tableId       表ID
     * @param tenantId      租户ID
     * @param matchPolicies 匹配类型枚举
     * @return 错误数据
     */
    public List<Map<String, Object>> importByExcel(FileDetailDto file,
                                                   CreateCustomerImportResponse response,
                                                   Map<String, String> fileTypeMap,
                                                   Long tableId,
                                                   String tenantId,
                                                   MatchPoliciesEnum matchPolicies) {
        List<Map<String, Object>> errors = new ArrayList<>();
        BosObject bosObject = bosUtils.getObject(bosConfig.getBucket().getDataSync(), file.getBosKey());
        if (bosObject == null) {
            response.getMessage().add(file.getFileName() + "文件不存在");
            return errors;
        }
        List<String> requiredFields = tableRecordCommonService.getRequiredFields(tableId);
        Map<String, String> encryptFields = tableRecordCommonService.getEncryptFields(tableId);
        CompletionService<Void> completionService = new ExecutorCompletionService<>(executorService);
        AtomicInteger atomicCount = new AtomicInteger(0);
        try (InputStream inputStream = bosObject.getObjectContent()) {
            // 自定义行处理逻辑
            ExcelUtil.readBySax(inputStream, 0, new RowHandler() {
                private final List<String> sourceNames = new ArrayList<>();
                private int count = 0;

                @Override
                public void handle(int i, long l, List<Object> list) {
                    count++;
                    // 如果是第一行，获取英文字段名
                    if (count == 1) {
                        list.forEach(name -> sourceNames.add(name.toString().replaceAll("[\\s\\n\\r\\t]", StrUtil.EMPTY)));
                    }
                    // 业务数据行且长度符合
                    int size = list.size();
                    if (count >= 2) {
                        Map<String, Object> excelData = new HashMap<>();
                        for (int i1 = 0; i1 < sourceNames.size(); i1++) {
                            String name = sourceNames.get(i1);
                            Object dataObj = size >= (i1 + 1) ? list.get(i1) : null;
                            if (dataObj != null && StrUtil.isNotBlank(dataObj.toString())) {
                                // 映射字段，做必要值类型转换
                                excelData.put(name, dorisService.covertDorisValue(fileTypeMap.get(name), dataObj));
                            }
                        }
                        // 异步校验数据，下发kafka，统计失败&成功数量
                        completionService.submit(() ->
                                saveData(excelData, errors, response, tenantId, encryptFields, matchPolicies, requiredFields, fileTypeMap));
                        atomicCount.incrementAndGet();
                    }
                }
            });
        } catch (Exception e) {
            response.getMessage().add(file.getFileName() + "客群导入异常");
            log.error("客群excel导入异常: IOException,", e);
        }
        // 等待线程执行完成，主要是获取成功失败条数
        for (int i = 0; i < atomicCount.get(); i++) {
            try {
                // 避免计数异常导致阻塞
                completionService.poll(3, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("客群导入{}线程执行异常", file.getFileName(), e);
            }
        }
        log.info("{} 客群文件导入结束", file.getFileName());
        return errors;

    }

    /**
     * 导入csv文件客群数据
     *
     * @param file          file
     * @param response      响应
     * @param fileTypeMap   字段类型map
     * @param tableId       表ID
     * @param tenantId      租户ID
     * @param matchPolicies 匹配类型枚举
     * @return 错误数据
     */
    public List<Map<String, Object>> importByCsv(FileDetailDto file,
                                                 CreateCustomerImportResponse response,
                                                 Map<String, String> fileTypeMap,
                                                 Long tableId,
                                                 String tenantId,
                                                 MatchPoliciesEnum matchPolicies) {
        List<Map<String, Object>> errors = new ArrayList<>();
        BosObject bosObject = bosUtils.getObject(bosConfig.getBucket().getDataSync(), file.getBosKey());
        if (bosObject == null) {
            response.getMessage().add(file.getFileName() + "文件不存在");
            return errors;
        }
        List<String> requiredFields = tableRecordCommonService.getRequiredFields(tableId);
        Map<String, String> encryptFields = tableRecordCommonService.getEncryptFields(tableId);
        CompletionService<Void> completionService = new ExecutorCompletionService<>(executorService);
        AtomicInteger atomicCount = new AtomicInteger(0);
        try (InputStream inputStream = bosObject.getObjectContent();
             InputStreamReader reader = new InputStreamReader(inputStream);) {
            CsvReader csvReader = CsvUtil.getReader(reader);
            AtomicInteger count = new AtomicInteger(0);
            List<String> enNames = new ArrayList<>();
            csvReader.read(csvRow -> {
                int andGet = count.incrementAndGet();
                if (andGet == 1) {
                    csvRow.getRawList().forEach(name -> enNames.add(name.replaceAll("[\\s\\n\\r\\t]", StrUtil.EMPTY)));
                }
                if (andGet >= 2) {
                    List<String> rawList = csvRow.getRawList();
                    int size = rawList.size();
                    Map<String, Object> itemMap = new HashMap<>();
                    for (int i = 0; i < enNames.size(); i++) {
                        String dataStr = size >= (i + 1) ? rawList.get(i) : null;
                        String name = enNames.get(i);
                        if (StrUtil.isNotBlank(dataStr)) {
                            // 映射字段，做必要值类型转换
                            itemMap.put(name, dorisService.covertDorisValue(fileTypeMap.get(name), dataStr));
                        }
                    }
                    // 异步校验数据，下发kafka，统计失败&成功数量
                    completionService.submit(() ->
                            saveData(itemMap, errors, response, tenantId, encryptFields, matchPolicies, requiredFields, fileTypeMap));
                    atomicCount.incrementAndGet();
                }
            });
        } catch (Exception e) {
            response.getMessage().add(file.getFileName() + "客群导入异常");
            log.error("客群csv导入异常: IOException,", e);
        }
        // 等待线程执行完成，主要是获取成功失败条数
        for (int i = 0; i < atomicCount.get(); i++) {
            try {
                // 避免计数异常导致阻塞
                completionService.poll(3, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                response.getMessage().add("客群导入线程执行异常");
                log.error("客群导入{}线程执行异常", file.getFileName(), e);
            }
        }
        log.info("{} 客群文件导入结束", file.getFileName());
        return errors;
    }

    /**
     * 下发存储数据
     * 根据匹配规则进行数据下发
     * ALL：保留全部数据，ONLY_MATCH：只保留匹配到的数据
     * 根据数据itemMap进行多字段逻辑或匹配
     * 匹配到数据，则添加客群标记字段，值为1，下发Kafka
     * 未匹配到数据，判断是否有userid，无则添加雪花算法ID值，并经过字段检验、加密后下发kafka
     *
     * @param itemMap        数据详情
     * @param errors         错误信息
     * @param response       返回
     * @param tenantId       租户ID
     * @param encryptFields  加密字段信息
     * @param matchPolicies  匹配策略
     * @param requiredFields 必填字段
     * @param fileTypeMap    字段类型
     * @return void
     */
    public Void saveData(Map<String, Object> itemMap,
                         List<Map<String, Object>> errors,
                         CreateCustomerImportResponse response,
                         String tenantId,
                         Map<String, String> encryptFields,
                         MatchPoliciesEnum matchPolicies,
                         List<String> requiredFields,
                         Map<String, String> fileTypeMap) {
        try {
            // 匹配数据
            // 数据加密后查询
            if (CollUtil.isEmpty(itemMap)) {
                return null;
            }
            Map<String, Object> map = new HashMap<>(itemMap);
            Map<String, Object> encryptItem = tableRecordCommonService.encryptItem(map, encryptFields);
            String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
            StringBuilder builder = new StringBuilder("(");
            for (Map.Entry<String, Object> entry : encryptItem.entrySet()) {
                if (response.getInterNames().contains(entry.getKey())) {
                    builder.append(" OR ")
                            .append(StringEscapeUtils.escapeSql(entry.getKey()))
                            .append(" = '")
                            .append(StringEscapeUtils.escapeSql((String) entry.getValue()))
                            .append("'");
                }
            }
            builder.append(")");
            String where = builder.toString().replaceFirst("OR", StrUtil.EMPTY);
            String sql = String.format("SELECT * FROM %s WHERE %s limit 1", mockUserTableName, where);
            List<Map<String, Object>> maps = dorisService.selectList(sql);
            String fieldName = genFieldName(response.getCustomerGroupId());
            if (CollUtil.isNotEmpty(maps)) {
                // 匹配到数据，只需将客群标记字段赋值为1
                Map<String, Object> oldData = maps.get(0);
                oldData.put(fieldName, "1");
                TableRecordMsgDTO recordMsg = new TableRecordMsgDTO();
                recordMsg.setCode(mockUserTableName);
                recordMsg.setData(oldData);
                String msg = JSONUtil.toJsonStr(recordMsg);
                kafkaTemplate.send(topic, msg);
            } else if (Objects.equals(matchPolicies, MatchPoliciesEnum.ALL)) {
                // 未匹配到，且策略为全部保留新增
                Object userId = itemMap.get("user_id");
                if (userId == null || StrUtil.isBlank(userId.toString())) {
                    itemMap.put("user_id", IdUtil.getSnowflakeNextIdStr());
                }
                // 数据检查、加密
                tableRecordCommonService.recordCheck(itemMap, requiredFields, fileTypeMap, DbTypeEnum.DORIS_TYPE.getDbType());
                tableRecordCommonService.encryptItem(itemMap, encryptFields);
                itemMap.put(fieldName, "1");
                TableRecordMsgDTO recordMsg = new TableRecordMsgDTO();
                recordMsg.setCode(mockUserTableName);
                recordMsg.setData(itemMap);
                String msg = JSONUtil.toJsonStr(recordMsg);
                kafkaTemplate.send(topic, msg);
            }
            response.getSuccessCount().incrementAndGet();
        } catch (DeepSightException.ParamsErrorException exception) {
            itemMap.put("deeepSightErrorMsg", exception.getMessage());
            errors.add(itemMap);
            response.getFailCount().incrementAndGet();
            log.error("下发客群导入数据检查异常，", exception);
        } catch (Exception e) {
            errors.add(itemMap);
            response.getFailCount().incrementAndGet();
            log.error("下发客群导入数据异常，", e);
        }
        return null;
    }

    /**
     * 数据检查
     *
     * @param files
     * @param dataSync
     * @return
     */
    @NotNull
    public List<String> checkData(List<FileDetailDto> files, String dataSync) {
        List<String> enNames = new ArrayList<>();
        Set<String> fileNames = new HashSet<>();
        for (FileDetailDto file : files) {
            // 同批次重名校验
            String fileName = file.getFileName();
            String bosKey = file.getBosKey();
            if (fileNames.contains(fileName) || fileNames.contains(bosKey)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "文件重复");
            }
            fileNames.add(fileName);
            fileNames.add(bosKey);
            // 首行字段校验
            String[] split = bosKey.split("\\.");
            String suffix = split[split.length - 1];
            if (!suffixNames.contains(suffix)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "文件扩展名错误，仅支持:" + suffixNames);
            }
            List<String> names = getEnNames(dataSync, bosKey, suffix);
            if (CollUtil.isEmpty(enNames)) {
                enNames.addAll(names);
            } else {
                if (enNames.size() != names.size()) {
                    throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "文件字段数量不一致");
                }
                for (int i = 0; i < names.size(); i++) {
                    if (!Objects.equals(enNames.get(i), names.get(i))) {
                        throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "文件字段名不一致");
                    }
                }
            }
        }
        return enNames;
    }

    /**
     * 获取首行字段
     *
     * @param buckName bos桶名
     * @param bosKey   bos文件key
     * @param suffix   文件后缀
     * @return 首行字段数组
     */
    public List<String> getEnNames(String buckName, String bosKey, String suffix) {
        Set<String> enNames = new HashSet<>();
        try (BosObject bosObject = bosUtils.getObject(buckName, bosKey);
             InputStream inputStream = bosObject.getObjectContent()) {
            if ("csv".equals(suffix)) {
                try (InputStreamReader reader = new InputStreamReader(inputStream);) {
                    CsvReader csvReader = CsvUtil.getReader(reader);
                    csvReader.read(csvRow -> csvRow.getRawList().forEach(enName -> enNames.add(enName.replaceAll("[\\s\\n\\r\\t]", StrUtil.EMPTY))));
                }
            } else {
                try (ExcelReader reader = ExcelUtil.getReader(inputStream, 0);) {
                    List<List<Object>> read = reader.read(0, 0);
                    Assert.notEmpty(read, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "数据行数错误，需大于等于3行"));
                    read.get(0).forEach(enName -> {
                        String enNameStr = enName.toString();
                        if (StrUtil.isNotBlank(enNameStr)) {
                            enNames.add(enNameStr.replaceAll("[\\s\\n\\r\\t]", StrUtil.EMPTY));
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("读取文件首行字段名异常，", e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "获取首行字段名异常");
        }
        return new ArrayList<>(enNames);
    }

    @Override
    @Cacheable(value = "customerGroupUserCount", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public Long getUserCount(String tenantId, Long groupId) {
        String tableName = TenantUtils.generateMockUserTableName(tenantId);
        String fieldName = genFieldName(groupId);
        String sql = String.format("SELECT COUNT(*) FROM %s WHERE %s = '1'", tableName, fieldName);
        try {
            return dorisService.getCount(sql);
        } catch (Exception e) {
            log.error("getUserCount查询用户数量失败，sql:{}", sql);
            return 0L;
        }
    }

    @Override
    @Cacheable(value = "customerGroupSetsUserCount", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public Long getUserCount(String tenantId, Set<Long> groupIds) {
        if (groupIds.isEmpty()) {
            return 0L;
        }
        String tableName = TenantUtils.generateMockUserTableName(tenantId);
        DqlParseResult dqlParseResult = new DqlParseResult();
        dqlParseResult.setRelation(LogicEnum.OR);
        dqlParseResult.setFrom(tableName);
        for (Long groupId : groupIds) {
            dqlParseResult.getWhere().add(genFieldName(groupId) + " = '1'");
        }
        String countSql = dqlParseResult.parseCountSql();
        try {
            return dorisService.getCount(countSql);
        } catch (Exception e) {
            log.error("getUserCount-Multi查询用户数量失败，sql:{}", countSql);
            return 0L;
        }
    }

    @Override
    public List<GroupDetailDto> getAllGroupList(String tenantId) {
        List<GroupDetailDto> list = new ArrayList<>();
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andTenantIdEqualTo(tenantId);
        List<CustomerGroup> customerGroups = customerGroupMapper.selectByExample(customerGroupCriteria);
        if (CollUtil.isEmpty(customerGroups)) {
            return list;
        }
        // 从mock_user统计客群人数
        for (CustomerGroup customerGroup : customerGroups) {
            Long groupId = customerGroup.getId();
            GroupDetailDto groupDetailDto = new GroupDetailDto(customerGroup);
            Long count = getUserCount(tenantId, groupId);
            groupDetailDto.setCount(count);
            list.add(groupDetailDto);
        }
        return list;
    }

    public CustomerGroup newDiffusionCustomerGroup(String tenantId, String userId, String customerGroupName) {
        // 检查人群扩散客群是否已存在
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andCustomerGroupNameEqualTo(customerGroupName);
        List<CustomerGroup> customerGroups = customerGroupMapper.selectByExample(customerGroupCriteria);
        if (CollectionUtils.isNotEmpty(customerGroups)) {
            return customerGroups.get(0);
        }

        CustomerGroup customerGroup = new CustomerGroup();
        customerGroup.setUserId(userId);
        customerGroup.setTenantId(tenantId);
        customerGroup.setCustomerGroupName(customerGroupName);
        customerGroup.setCustomerGroupDescription("");
        customerGroup.setCustomerGroupValueUpdateMod(UpdateModEnum.REPLACE.getCode());
        customerGroup.setTriggerMod(TriggerModeEnum.MANUAL.getCode());
        customerGroup.setCustomerGroupRule("");
        customerGroup.setCalStatus(TaskExecStatusEnum.SUCCESS.getCode());
        customerGroup.setTask(0L);

        customerGroup.setDel(DelEnum.NOT_DELETED.getBoolean());
        customerGroup.setCreator(WebContextHolder.getUserAuthInfo().getUserName());
        customerGroup.setModifier(WebContextHolder.getUserAuthInfo().getUserName());
        Date now = new Date();
        customerGroup.setCreateTime(now);
        customerGroup.setUpdateTime(now);
        customerGroup.setGroupingType(GroupingTypeEnum.MODEL_PREDICTION.getCode());
        customerGroup.setConfigTag(false); // 不可变更配置

        try {
            customerGroupMapper.insert(customerGroup);
            // 创建客群宽表字段
            newDorisField(tenantId, genFieldName(customerGroup.getId()));
        } catch (Exception e) {
            log.error("CustomerGroupService.newDiffusionCustomerGroup error : ", e);
            return null;
        }
        return customerGroup;
    }
}
