package com.baidu.keyue.deepsight.service.sop.impl;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.AiobRobotTypeEnum;
import com.baidu.keyue.deepsight.enums.SopKnowledgeTypeEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisResultAggResponse;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisResultQueryRequest;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisResultResponse;
import com.baidu.keyue.deepsight.service.sop.SopAnalysisResultService;
import com.baidu.keyue.deepsight.service.sop.SopTaskTagEnum;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SOP知识引用分析结果服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SopAnalysisResultServiceImpl implements SopAnalysisResultService {

    private final DorisService dorisService;

    @Override
    public BasePageResponse.Page<SopAnalysisResultResponse> queryAnalysisResults(String tenantId,
                                                                                 SopAnalysisResultQueryRequest request) {
        try {
            // 构建查询条件
            String whereClause = buildWhereClause(request);
            String tableName = TenantUtils.generateSopAnalysisResultTableName(tenantId);

            // 查询总数
            long totalCount = queryTotalCount(tableName, whereClause);
            if (totalCount == 0) {
                return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, new ArrayList<>());
            }

            // 分页查询数据并计算统计信息
            List<SopAnalysisResultResponse> results = queryPageDataWithStats(tenantId, request, whereClause,
                    totalCount);

            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), totalCount, results);

        } catch (Exception e) {
            log.error("查询SOP知识引用分析结果失败", e);
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST,
                    "查询SOP知识引用分析结果失败");
        }
    }

    @Override
    public BaseResponse<SopAnalysisResultAggResponse> queryAnalysisTopResults(String tenantId,
                                                                              SopAnalysisResultQueryRequest request) {

        // 获取faqTop 5
        // 构建查询条件
        try {
            SopAnalysisResultAggResponse aggResponse = new SopAnalysisResultAggResponse();
            String whereClause = buildWhereClause(request);
            String tableName = TenantUtils.generateSopAnalysisResultTableName(tenantId);

            // 查询总数
            long totalCount = queryTotalCount(tableName, whereClause);
            if (totalCount == 0) {
                return BaseResponse.of(new SopAnalysisResultAggResponse());
            }

            // 分页查询数据并计算统计信息
            request.setPageNo(1);
            request.setPageSize(5);
            List<SopAnalysisResultResponse> results = queryPageDataWithStats(tenantId, request, whereClause,
                    totalCount);
            aggResponse.setFaqSopTopAnalysis(results);


            // 获取有意向意图top20
            Map<String, Integer> 	intention = queryIntentAggTop(TenantUtils.generateSopAnalysisResultTableName(tenantId),
                    SopKnowledgeTypeEnum.INTENT, List.of(SopTaskTagEnum.A.getTagName(),
                            SopTaskTagEnum.B.getTagName()), 20, false);
            aggResponse.setIntention(intention);
            // 获取无意向意图top20
            Map<String, Integer> noIntention = queryIntentAggTop(TenantUtils.generateSopAnalysisResultTableName(tenantId),
                    SopKnowledgeTypeEnum.INTENT, List.of(SopTaskTagEnum.A.getTagName(),
                            SopTaskTagEnum.B.getTagName()), 20, true);
            aggResponse.setNoIntention(noIntention);

            return BaseResponse.of(aggResponse);
        } catch (Exception e) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST,
                    "查询SOP知识引用分析结果失败");
        }

    }

    /**
     * 构建WHERE子句
     */
    private String buildWhereClause(SopAnalysisResultQueryRequest request) {
        List<String> conditions = new ArrayList<>();

        // robotId 精确匹配
        if (StringUtils.hasText(request.getRobotId())) {
            conditions.add(String.format("robotId = '%s'", request.getRobotId()));
        }

        // taskId 精确匹配
        if (StringUtils.hasText(request.getTaskId())) {
            conditions.add(String.format("taskId = '%s'", request.getTaskId()));
        }

        // knowledgeTag 模糊查询
        if (StringUtils.hasText(request.getKnowledgeTag())) {
            conditions.add(String.format("LOCATE('%s', knowledgeTag) > 0", request.getKnowledgeTag()));
        }

        // robotVer 精确匹配
        if (StringUtils.hasText(request.getRobotVer())) {
            conditions.add(String.format("robotVer = '%s'", request.getRobotVer()));
        }

        // knowledgeType 精确匹配
        if (request.getKnowledgeType() != null) {
            conditions.add(String.format("knowledgeType = %d", request.getKnowledgeType()));
        }

        // keyword 在知识内容中模糊查询
        if (StringUtils.hasText(request.getKeyword())) {
            conditions.add(String.format("LOCATE('%s', knowledgeText) > 0", request.getKeyword()));
        }

        if (!conditions.isEmpty()) {
            return " WHERE " + String.join(" AND ", conditions);
        }
        return "";
    }


    /**
     * 查询聚合统计数据，并返回意图分组的结果
     * @param tableName 表名
     * @param tagType 标签类型 有意向跟无意向
     * @return
     */
    private Map<String, Integer> queryIntentAggTop(String tableName,
                                                   SopKnowledgeTypeEnum tagType,
                                                   List<String> tagName,
                                                   Integer topN,
                                                   boolean notIn) {
        // 根据参数决定 IN 还是 NOT IN
        String inClause = notIn ? "NOT IN" : "IN";

        String sql = String.format(
                "SELECT knowledgeText AS text, COUNT(*) AS cnt " +
                        "FROM %s " +
                        "WHERE knowledgeType = %d AND knowledgeTag %s (%s) " +
                        "GROUP BY knowledgeText " +
                        "ORDER BY cnt DESC " +
                        "LIMIT %d;",
                tableName,
                tagType.getCode(),
                inClause,
                tagName.stream().map(this::escapeSql).collect(Collectors.joining(",")),
                topN
        );

        return dorisService.selectList(sql).stream().collect(Collectors.toMap(
                row -> (String) row.get("text"),
                row -> (Integer) row.get("cnt")
        ));
    }

    /**
     * 查询总数
     */
    private long queryTotalCount(String tableName, String whereClause) {
        String countSql = String.format("SELECT COUNT(*) FROM %s %s", tableName, whereClause);
        return dorisService.getCount(countSql);
    }

    /**
     * 分页查询数据并计算统计信息
     */
    private List<SopAnalysisResultResponse> queryPageDataWithStats(
            String tenantId,
            SopAnalysisResultQueryRequest request,
            String whereClause,
            long totalCount) {

        // 先统计全表组合次数，再应用过滤条件
        int offset = (request.getPageNo() - 1) * request.getPageSize();
        String sql = String.format("""
                SELECT *
                FROM (
                  SELECT
                    d.*,
                    COUNT(*) OVER (
                      PARTITION BY taskId, robotId, robotVer, knowledgeText, knowledgeType, nodeId, topicId
                    ) AS count
                  FROM %s d
                ) t
                %s
                ORDER BY t.count DESC, t.createTime DESC
                LIMIT %d, %d
                """, TenantUtils.generateSopAnalysisResultTableName(tenantId), whereClause, offset,
                request.getPageSize());

        // 执行查询
        List<Map<String, Object>> rows = dorisService.selectList(sql);

        // 转换为响应对象并计算概率
        return rows.stream().map(row -> {
            SopAnalysisResultResponse response = new SopAnalysisResultResponse();
            response.setTaskId((String) row.get("taskId"));
            response.setRobotId((String) row.get("robotId"));
            response.setRobotVer((String) row.get("robotVer"));
            response.setTopicId((String) row.get("topicId"));
            response.setNodeId((String) row.get("nodeId"));

            // 处理knowledgeType的类型转换
            Object knowledgeTypeObj = row.get("knowledgeType");
            Integer knowledgeType = null;
            if (knowledgeTypeObj != null) {
                if (knowledgeTypeObj instanceof Number) {
                    knowledgeType = ((Number) knowledgeTypeObj).intValue();
                } else if (knowledgeTypeObj instanceof String) {
                    knowledgeType = Integer.parseInt((String) knowledgeTypeObj);
                }
            }
            response.setKnowledgeType(knowledgeType);

            response.setKnowledgeText((String) row.get("knowledgeText"));
            response.setQueryId((String) row.get("queryId"));
            response.setSessionId((String) row.get("sessionId"));
            response.setKnowledgeDirId((String) row.get("knowledgeDirId"));
            response.setKnowledgetTag((String) row.get("knowledgeTag"));
            response.setContent((String) row.get("content"));

            // 处理时间字段
            Object createTimeObj = row.get("createTime");
            if (createTimeObj != null) {
                if (createTimeObj instanceof java.sql.Timestamp) {
                    response.setCreateTime(((java.sql.Timestamp) createTimeObj).toLocalDateTime());
                } else if (createTimeObj instanceof LocalDateTime) {
                    response.setCreateTime((LocalDateTime) createTimeObj);
                }
            }

            Object updateTimeObj = row.get("updateTime");
            if (updateTimeObj != null) {
                if (updateTimeObj instanceof java.sql.Timestamp) {
                    response.setUpdateTime(((java.sql.Timestamp) updateTimeObj).toLocalDateTime());
                } else if (updateTimeObj instanceof LocalDateTime) {
                    response.setUpdateTime((LocalDateTime) updateTimeObj);
                }
            }

            // 设置统计信息
            Long count = ((Number) row.get("count")).longValue();
            response.setCount(count);

            // 计算概率：当前组合出现次数 / 总记录数 * 100
            BigDecimal probability = BigDecimal.valueOf(count)
                    .divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            response.setProbability(probability);

            // 设置知识类型描述
            response.setKnowledgeTypeDesc(SopKnowledgeTypeEnum.getDescByCode(response.getKnowledgeType()));

            return response;
        }).collect(Collectors.toList());
    }


    /**
     * @param tenantId
     * @param taskId
     * @param robotId
     * @param robotVer
     * @param analysisTime
     */
    @Override
    public void createAnalysisResults(String tenantId, String taskId, String robotId, String robotVer,
                                      AiobRobotTypeEnum aiobRobotTypeEnum, Date analysisTime) {
        // 1) 快捷场景
        if (AiobRobotTypeEnum.QUICK_SCENES.equals(aiobRobotTypeEnum)) {


            // 2) 时间参数：以 analysisTime 为起始
            String startTime = DatetimeUtils.formatDate(
                    analysisTime == null ? new Date() : analysisTime
            );

            // 3) SQL 模板
            String countTpl = """
                    SELECT COUNT(1)
                    FROM aiob_conversation_record_%s r
                    JOIN (
                      SELECT sessionId
                      FROM aiob_conversation_session_service_%s
                      WHERE (is_auto_answer != 1 OR is_auto_answer IS NULL)
                      GROUP BY sessionId
                    ) s ON r.sessionId = s.sessionId
                    WHERE r.roleType = 'voice'
                      AND r.deepsight_datetime < '%s'
                    """;


            String pageTpl = """
                    SELECT r.*, s.*
                    FROM aiob_conversation_record_%s r
                    JOIN (
                      SELECT sessionId, customTagList, is_auto_answer, startTime, endTime
                      FROM aiob_conversation_session_service_%s
                      WHERE (is_auto_answer != 1 OR is_auto_answer IS NULL)
                    ) s ON r.sessionId = s.sessionId
                    WHERE r.roleType = 'voice'
                      AND r.deepsight_datetime < '%s'
                      AND (
                        r.deepsight_datetime < '%s'
                        OR (r.deepsight_datetime = '%s' AND r.sessionId < '%s')
                      )
                    ORDER BY r.deepsight_datetime DESC, r.sessionId DESC
                    LIMIT %d
                    """;

            // 4) 执行计数
            String countSql = String.format(countTpl, tenantId, tenantId, startTime);
            long total = dorisService.getCount(countSql);

            // 5) 分页拉取
            int pageSize = 2000;
            String cursorDt = startTime;
            String cursorSid = "";

            while (true) {
                String dataSql = String.format(pageTpl, tenantId, tenantId, startTime,
                        cursorDt, cursorDt, cursorSid, pageSize);

                List<Map<String, Object>> rows = dorisService.selectList(dataSql);
                if (rows == null || rows.isEmpty()) {
                    break;
                }

                // 6) 处理一页数据（解析知识引用等）
                List<String> insertSqlList = new ArrayList<>();
                for (Map<String, Object> row : rows) {
                    // 解析并构建批量插入SQL
                    insertSqlList.addAll(buildInsertSqlForRow(tenantId, taskId, robotId, robotVer, row));
                }
                
                // 批量执行插入
                if (!insertSqlList.isEmpty()) {
                    batchInsertAnalysisResults(insertSqlList);
                }

                // 7) 更新游标：取本页最后一行的排序键
                Map<String, Object> last = rows.get(rows.size() - 1);
                cursorDt = Objects.toString(last.get("deepsight_datetime"), cursorDt);
                cursorSid = Objects.toString(last.get("sessionId"), cursorSid);
            }
        }
    }

    /**
     * 为单行数据构建插入SQL列表
     * 处理 r.faq + r.dirId 以及 s.customTagList 的批量插入逻辑
     */
    private List<String> buildInsertSqlForRow(String tenantId, String taskId, String robotId, String robotVer, 
                                            Map<String, Object> row) {
        List<String> insertSqlList = new ArrayList<>();
        
        // 获取基础字段
        String topicId = Objects.toString(row.get("topicId"), "");
        String nodeId = Objects.toString(row.get("nodeId"), "");
        String queryId = Objects.toString(row.get("queryId"), "");
        String sessionId = Objects.toString(row.get("sessionId"), "");
        String content = Objects.toString(row.get("content"), "");
        
        // 获取时间字段
        String createTime = getCurrentTimestamp();
        String updateTime = getCurrentTimestamp();
        
        // 处理 r.faq 和 r.dirId 的一对一对应关系
        String faqStr = Objects.toString(row.get("faq"), "");
        String dirIdStr = Objects.toString(row.get("dirId"), "");
        
        if (StringUtils.hasText(faqStr) && StringUtils.hasText(dirIdStr)) {
            List<String> faqList = parseListFromString(faqStr);
            List<String> dirIdList = parseListFromString(dirIdStr);
            
            // 确保两个列表长度一致，取较小的长度
            int minSize = Math.min(faqList.size(), dirIdList.size());
            for (int i = 0; i < minSize; i++) {
                String faqText = faqList.get(i);
                String knowledgeDirId = dirIdList.get(i);
                
                if (StringUtils.hasText(faqText)) {
                    String insertSql = buildSingleInsertSql(tenantId, taskId, robotId, robotVer,
                            topicId, nodeId, 2, faqText, queryId, sessionId, knowledgeDirId, 
                            "", content, createTime, updateTime);
                    insertSqlList.add(insertSql);
                }
            }
        }
        
        // 处理 s.customTagList
        String customTagListStr = Objects.toString(row.get("customTagList"), "");
        if (StringUtils.hasText(customTagListStr)) {
            List<String> customTagList = parseListFromString(customTagListStr);
            
            for (String customTag : customTagList) {
                if (StringUtils.hasText(customTag)) {
                    String insertSql = buildSingleInsertSql(tenantId, taskId, robotId, robotVer,
                            topicId, nodeId, 3, customTag, queryId, sessionId, "", 
                            customTag, content, createTime, updateTime);
                    insertSqlList.add(insertSql);
                }
            }
        }
        
        return insertSqlList;
    }

    /**
     * 构建单条插入SQL
     */
    private String buildSingleInsertSql(String tenantId, String taskId, String robotId, String robotVer,
                                      String topicId, String nodeId, int knowledgeType, String knowledgeText,
                                      String queryId, String sessionId, String knowledgeDirId, String knowledgeTag,
                                      String content, String createTime, String updateTime) {
        return String.format("""
            INSERT INTO aiob_sop_analysis_result_%s 
            (taskId, robotId, robotVer, topicId, nodeId, knowledgeType, knowledgeText, 
             queryId, sessionId, knowledgeDirId, knowledgeTag, createTime, updateTime, content)
            VALUES ('%s', '%s', '%s', '%s', '%s', %d, '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
            """, tenantId, escapeSql(taskId), escapeSql(robotId), escapeSql(robotVer), 
                 escapeSql(topicId), escapeSql(nodeId), knowledgeType, escapeSql(knowledgeText),
                 escapeSql(queryId), escapeSql(sessionId), escapeSql(knowledgeDirId), 
                 escapeSql(knowledgeTag), createTime, updateTime, escapeSql(content));
    }

    /**
     * 批量执行插入SQL
     */
    private void batchInsertAnalysisResults(List<String> insertSqlList) {
        try {
            for (String sql : insertSqlList) {
                dorisService.execSql(sql);
            }
        } catch (Exception e) {
            log.error("批量插入SOP分析结果失败", e);
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "批量插入SOP分析结果失败");
        }
    }

    /**
     * 解析字符串列表（假设格式为逗号分隔或JSON数组）
     */
    private List<String> parseListFromString(String listStr) {
        if (!StringUtils.hasText(listStr)) {
            return new ArrayList<>();
        }
        
        // 尝试JSON数组解析
        if (listStr.trim().startsWith("[") && listStr.trim().endsWith("]")) {
            try {
                // 简单的JSON数组解析（去掉括号，按逗号分割）
                String content = listStr.trim().substring(1, listStr.trim().length() - 1);
                return Arrays.stream(content.split(","))
                        .map(s -> s.trim().replaceAll("^\"|\"$", ""))
                        .filter(StringUtils::hasText)
                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.warn("JSON数组解析失败，尝试逗号分割: {}", listStr, e);
            }
        }
        
        // 逗号分割解析
        return Arrays.stream(listStr.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }

    /**
     * SQL转义
     */
    private String escapeSql(String value) {
        if (value == null) {
            return "";
        }
        return value.replace("'", "''");
    }

    /**
     * 获取当前时间戳字符串
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
