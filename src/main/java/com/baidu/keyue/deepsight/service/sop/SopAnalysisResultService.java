package com.baidu.keyue.deepsight.service.sop;

import com.baidu.keyue.deepsight.enums.AiobRobotTypeEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisResultAggResponse;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisResultQueryRequest;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisResultResponse;

import java.util.Date;

/**
 * SOP知识引用分析结果服务接口
 */
public interface SopAnalysisResultService {

    /**
     * 分页查询SOP知识引用分析结果
     * 支持根据robotId、taskId、knowledgetTag、knowledgeType、robotVer进行模糊查询
     * 并计算统计信息（计数和概率）
     *
     * @param request 查询请求
     * @return 分页响应结果
     */
    BasePageResponse.Page<SopAnalysisResultResponse> queryAnalysisResults(String tenantId,
                                                                          SopAnalysisResultQueryRequest request);


    BaseResponse<SopAnalysisResultAggResponse> queryAnalysisTopResults(String tenantId,
                                                                       SopAnalysisResultQueryRequest request);




    /**
     * SOP知识引用分析创建
     * @param tenantId
     * @param taskId
     * @param robotId
     * @param robotVer
     */
    void createAnalysisResults(String tenantId, String taskId, String robotId, String robotVer,
                               AiobRobotTypeEnum aiobRobotTypeEnum, Date analysisTime);
}