package com.baidu.keyue.deepsight.service.builtin.impl;

import com.baidu.keyue.deepsight.config.BuiltinLabelProperties;
import com.baidu.keyue.deepsight.constants.BuiltinLabelConstants;
import com.baidu.keyue.deepsight.constants.RiskAssessmentConstants;
import com.baidu.keyue.deepsight.constants.SqlConstants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.BuiltinLabelEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.LabelDistribute;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.label.UpdateRecord;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelMapper;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelCalculateService;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.service.label.LabelCalculateService;
import com.baidu.keyue.deepsight.service.llm.LLMRiskAssessmentService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.service.label.LabelDistributionCalculator;
import com.baidu.keyue.deepsight.utils.LocalDateTimeUtils;
import com.baidu.keyue.deepsight.utils.SqlUtils;
import com.baidu.keyue.deepsight.utils.StringUtil;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 内置标签计算服务实现
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class BuiltinLabelCalculateServiceImpl implements BuiltinLabelCalculateService {

    private final LabelCalculateService labelCalculateService;
    private final DorisService dorisService;
    private final TaskInfoService taskInfoService;
    private final LabelMapper labelMapper;
    private final ObjectMapper objectMapper;
    private final LabelCatalogService labelCatalogService;
    private final LabelDistributionCalculator labelDistributionCalculator;
    private final BuiltinLabelProperties builtinLabelProperties;
    private final LLMRiskAssessmentService llmRiskAssessmentService;
    private final ExecutorService llmExecutorService;

    public BuiltinLabelCalculateServiceImpl(
            LabelCalculateService labelCalculateService,
            DorisService dorisService,
            TaskInfoService taskInfoService,
            LabelMapper labelMapper,
            ObjectMapper objectMapper,
            LabelCatalogService labelCatalogService,
            LabelDistributionCalculator labelDistributionCalculator,
            BuiltinLabelProperties builtinLabelProperties,
            LLMRiskAssessmentService llmRiskAssessmentService,
            @Qualifier("llmRiskAssessmentExecutor") ExecutorService llmExecutorService) {
        this.labelCalculateService = labelCalculateService;
        this.dorisService = dorisService;
        this.taskInfoService = taskInfoService;
        this.labelMapper = labelMapper;
        this.objectMapper = objectMapper;
        this.labelCatalogService = labelCatalogService;
        this.labelDistributionCalculator = labelDistributionCalculator;
        this.builtinLabelProperties = builtinLabelProperties;
        this.llmRiskAssessmentService = llmRiskAssessmentService;
        this.llmExecutorService = llmExecutorService;
    }

    @Override
    public void processStaticBuiltinLabel(String tenantId, LabelWithBLOBs label, BuiltinLabelEnum labelEnum) {
        log.info("开始处理静态内置标签: {} (租户: {})", labelEnum.getLabelName(), tenantId);

        try {
            // 特殊处理标签6（客服风险标签）
            if (labelEnum == BuiltinLabelEnum.CUSTOMER_SERVICE_RISK) {
                processCustomerServiceRiskLabel(tenantId, label);
                return;
            }

            // 标签1-3：复用现有标签计算逻辑
            TaskInfo taskInfo = taskInfoService.getTaskDetailWithId(label.getTask());
            if (taskInfo == null) {
                log.warn("标签 {} 对应的任务信息不存在，跳过处理", label.getLabelName());
                return;
            }

            // 调用现有的标签计算逻辑
            labelCalculateService.execByScheduler(label, taskInfo);

        } catch (Exception e) {
            log.error("处理静态内置标签失败: {} (租户: {})", labelEnum.getLabelName(), tenantId, e);
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR,
                    "静态内置标签处理失败: " + labelEnum.getLabelName());
        }
    }

    @Override
    public void processDynamicIntentionLabel(String tenantId, LabelWithBLOBs label) {
        log.info("开始处理动态意向标签: {} (租户: {})", label.getLabelName(), tenantId);

        try {
            // 提取机器人名称
            String robotName = extractRobotNameFromIntentionLabel(label.getLabelName());
            if (StringUtils.isBlank(robotName)) {
                log.warn("无法从标签名称中提取机器人名称: {}", label.getLabelName());
                return;
            }

            // 更新标签状态为运行中
            updateLabelStatus(label.getId(), TaskExecStatusEnum.RUNNING);

            // 查询会话数据并处理customTagList（只查最新一条记录）
            String sessionTableName = String.format(BuiltinLabelConstants.DynamicCatalog.SESSION_TABLE_TEMPLATE, tenantId);
            String sql = SqlUtils.buildIntentionLabelLatestSQL(sessionTableName, robotName);

            List<Map<String, Object>> sessionData = dorisService.selectList(sql);
            if (CollectionUtils.isEmpty(sessionData)) {
                log.info("没有找到机器人 {} 的会话数据", robotName);
                updateLabelStatus(label.getId(), TaskExecStatusEnum.SUCCESS);
                return;
            }

            // 处理意向标签数据
            processIntentionTagData(tenantId, label, sessionData);

            // 计算并更新标签状态为成功（包含distribution）
            String distribution = calculateBuiltinLabelDistribution(tenantId, label);
            updateLabelStatusWithDistribution(label.getId(), TaskExecStatusEnum.SUCCESS, distribution);

        } catch (Exception e) {
            log.error("处理动态意向标签失败: {} (租户: {})", label.getLabelName(), tenantId, e);
            updateLabelStatus(label.getId(), TaskExecStatusEnum.FAILED);
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR,
                    "动态意向标签处理失败: " + label.getLabelName());
        }
    }

    @Override
    public void processDynamicLLMLabel(String tenantId, LabelWithBLOBs label) {
        log.info("开始处理动态LLM标签: {} (租户: {})", label.getLabelName(), tenantId);

        try {
            // 更新标签状态为运行中
            updateLabelStatus(label.getId(), TaskExecStatusEnum.RUNNING);

            // 通过catalogId获取robotName（三级目录名称就是robotName）
            String robotName = getRobotNameByCatalogId(tenantId, label.getCatalogId());
            if (StringUtils.isBlank(robotName)) {
                log.warn("无法获取标签 {} 对应的robotName，catalogId: {}", label.getLabelName(), label.getCatalogId());
                updateLabelStatus(label.getId(), TaskExecStatusEnum.FAILED);
                return;
            }

            // 查询会话数据并处理tagExtractInfo（根据robotName过滤，只查最新一条记录）
            String sessionTableName = String.format(BuiltinLabelConstants.DynamicCatalog.SESSION_TABLE_TEMPLATE, tenantId);
            String sql = SqlUtils.buildLLMLabelLatestSQL(sessionTableName, robotName, label.getLabelName());

            List<Map<String, Object>> sessionData = dorisService.selectList(sql);
            if (CollectionUtils.isEmpty(sessionData)) {
                log.info("没有找到标签 {} 的会话数据 (robotName: {})", label.getLabelName(), robotName);
                updateLabelStatus(label.getId(), TaskExecStatusEnum.SUCCESS);
                return;
            }

            // 处理LLM标签数据
            processLLMTagData(tenantId, label, sessionData, label.getLabelName());

            // 计算并更新标签状态为成功（包含distribution）
            String distribution = calculateBuiltinLabelDistribution(tenantId, label);
            updateLabelStatusWithDistribution(label.getId(), TaskExecStatusEnum.SUCCESS, distribution);

        } catch (Exception e) {
            log.error("处理动态LLM标签失败: {} (租户: {})", label.getLabelName(), tenantId, e);
            updateLabelStatus(label.getId(), TaskExecStatusEnum.FAILED);
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR,
                    "动态LLM标签处理失败: " + label.getLabelName());
        }
    }

    @Override
    public void processCustomerServiceRiskLabel(String tenantId, LabelWithBLOBs label) {
        log.info("开始处理客服风险标签: {} (租户: {})", label.getLabelName(), tenantId);

        try {
            // 更新标签状态为运行中
            updateLabelStatus(label.getId(), TaskExecStatusEnum.RUNNING);

            // 查询会话数据
            String sessionTableName = String.format(BuiltinLabelConstants.DynamicCatalog.SESSION_TABLE_TEMPLATE, tenantId);

            // 1. 查询最近通话时间数据
            String timeSql = SqlUtils.buildRiskTimeDataSQL(sessionTableName);
            List<Map<String, Object>> timeData = dorisService.selectList(timeSql);

            // 2. 查询对话内容数据
            String contentSql = SqlUtils.buildRiskContentDataSQL(sessionTableName);
            List<Map<String, Object>> contentData = dorisService.selectList(contentSql);

            if (CollectionUtils.isEmpty(timeData) && CollectionUtils.isEmpty(contentData)) {
                log.info("没有找到风险评估的会话数据");
                updateLabelStatus(label.getId(), TaskExecStatusEnum.SUCCESS);
                return;
            }

            // 处理风险评估数据
            processRiskAssessmentData(tenantId, label, timeData, contentData);

            // 计算并更新标签状态为成功（包含distribution）
            String distribution = calculateBuiltinLabelDistribution(tenantId, label);
            updateLabelStatusWithDistribution(label.getId(), TaskExecStatusEnum.SUCCESS, distribution);

        } catch (Exception e) {
            log.error("处理客服风险标签失败: {} (租户: {})", label.getLabelName(), tenantId, e);
            updateLabelStatus(label.getId(), TaskExecStatusEnum.FAILED);
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR,
                    "客服风险标签处理失败: " + label.getLabelName());
        }
    }

    /**
     * 从意向标签名称中提取机器人名称
     *
     * @param labelName 标签名称，格式：robotName + INTENTION_LABEL_SUFFIX
     * @return 机器人名称
     * <AUTHOR>
     */
    private String extractRobotNameFromIntentionLabel(String labelName) {
        if (StringUtils.isBlank(labelName) || !labelName.endsWith(SqlConstants.StringConstants.INTENTION_LABEL_SUFFIX)) {
            return null;
        }
        return labelName.substring(0, labelName.length() - SqlConstants.StringConstants.INTENTION_LABEL_SUFFIX.length());
    }

    /**
     * 更新标签状态
     *
     * @param labelId 标签ID
     * @param status  状态
     * <AUTHOR>
     */
    private void updateLabelStatus(Long labelId, TaskExecStatusEnum status) {
        updateLabelStatusWithDistribution(labelId, status, null);
    }

    /**
     * 更新标签状态并计算distribution
     *
     * @param labelId 标签ID
     * @param status  状态
     * @param distribution 标签分布统计，如果为null则不更新distribution
     * <AUTHOR>
     */
    private void updateLabelStatusWithDistribution(Long labelId, TaskExecStatusEnum status, String distribution) {
        try {
            labelMapper.updateLabelCalTaskResult(labelId, status.getCode(), distribution, new Date(), false);
        } catch (Exception e) {
            // 其他异常，记录日志但不中断流程
            log.error("更新标签状态失败: labelId={}, status={}", labelId, status, e);
        }
    }

    /**
     * 处理意向标签数据（优化版本：批量处理）
     *
     * @param tenantId    租户ID
     * @param label       标签信息
     * @param sessionData 会话数据
     * <AUTHOR>
     */
    private void processIntentionTagData(String tenantId, LabelWithBLOBs label, List<Map<String, Object>> sessionData) {
        log.info("开始处理意向标签数据，数据量: {}", sessionData.size());

        String userTableName = TenantUtils.generateMockUserTableName(tenantId);
        if (label.getField() == null) {
            log.error("标签字段ID为空，无法处理意向标签数据: labelId={}", label.getId());
            return;
        }
        String fieldName = SqlConstants.StringConstants.LABEL_FIELD_PREFIX + label.getField();

        // 批量处理数据，避免单条更新的性能问题
        processBatchIntentionTagData(sessionData, userTableName, fieldName);
        
        log.info("意向标签数据处理完成");
    }

    /**
     * 批量处理意向标签数据
     *
     * @param sessionData   会话数据
     * @param userTableName 用户表名
     * @param fieldName     字段名
     * <AUTHOR>
     */
    private void processBatchIntentionTagData(List<Map<String, Object>> sessionData, String userTableName, String fieldName) {

        // 预处理数据，提取有效的更新记录
        List<UpdateRecord> updateRecords = new ArrayList<>();
        
        for (Map<String, Object> row : sessionData) {
            String oneId = (String) row.get(SqlConstants.FieldNames.ONE_ID);
            String customTagListJson = (String) row.get(SqlConstants.FieldNames.CUSTOM_TAG_LIST);

            if (StringUtils.isBlank(oneId) || StringUtils.isBlank(customTagListJson)) {
                continue;
            }

            try {
                // 解析customTagList JSON数组
                JsonNode customTagList = objectMapper.readTree(customTagListJson);
                if (customTagList.isArray() && !customTagList.isEmpty()) {
                    // 提取所有意向标签
                    List<String> intentionTags = new ArrayList<>();
                    for (JsonNode tagNode : customTagList) {
                        String tag = tagNode.asText();
                        if (StringUtils.isNotBlank(tag)) {
                            intentionTags.add(tag);
                        }
                    }

                    if (!intentionTags.isEmpty()) {
                        // 构建Doris数组格式：['tag1', 'tag2', 'tag3']
                        String arrayValue = buildDorisArrayValue(intentionTags);
                        updateRecords.add(new UpdateRecord(oneId, arrayValue));
                    }
                }
            } catch (Exception e) {
                log.error("解析用户 {} 的意向标签数据失败，跳过该用户: {}", oneId, customTagListJson, e);
            }
        }

        log.info("预处理完成，有效更新记录数: {}", updateRecords.size());

        // 分批执行更新
        executeBatchUpdate(updateRecords, userTableName, fieldName);
    }

    /**
     * 执行批量更新
     *
     * @param updateRecords 更新记录列表
     * @param userTableName 用户表名
     * @param fieldName     字段名
     * <AUTHOR>
     */
    private void executeBatchUpdate(List<UpdateRecord> updateRecords, String userTableName,
                                    String fieldName) {
        if (CollectionUtils.isEmpty(updateRecords)) {
            log.info("没有需要更新的记录");
            return;
        }

        int batchSize = builtinLabelProperties.getPerformance().getBatchSize();
        int totalBatches = (updateRecords.size() + batchSize - 1) / batchSize;
        log.info("开始批量更新，总记录数: {}, 批次大小: {}, 总批次数: {}", updateRecords.size(), batchSize, totalBatches);

        for (int i = 0; i < updateRecords.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, updateRecords.size());
            List<UpdateRecord> batch = updateRecords.subList(i, endIndex);
            int currentBatch = (i / batchSize) + 1;

            try {
                // 构建批量更新SQL
                String batchUpdateSql = buildBatchUpdateSql(batch, userTableName, fieldName);
                
                // 执行批量更新
                dorisService.execSql(batchUpdateSql);
                
                log.info("批次 {}/{} 更新完成，本批次记录数: {}", currentBatch, totalBatches, batch.size());
                
            } catch (Exception e) {
                log.error("批次 {}/{} 更新失败，本批次记录数: {}", currentBatch, totalBatches, batch.size(), e);
                throw new DeepSightException.DorisExecException("批量更新失败: " + e.getMessage());
            }
        }
        
        log.info("批量更新完成，总更新记录数: {}", updateRecords.size());
    }

    /**
     * 构建批量更新SQL（使用CASE WHEN优化）
     *
     * @param batch         批量记录
     * @param userTableName 用户表名
     * @param fieldName     字段名
     * @return 批量更新SQL
     * <AUTHOR>
     */
    private String buildBatchUpdateSql(List<UpdateRecord> batch, String userTableName, String fieldName) {
        if (batch.size() == 1) {
            // 单条记录直接使用简单UPDATE
            UpdateRecord record = batch.get(0);
            return String.format(
                SqlConstants.BuiltinLabelSql.UPDATE_USER_LABEL,
                userTableName, fieldName, record.getArrayValue(), record.getOneId()
            );
        }
        
        // 多条记录使用CASE WHEN批量更新
        return buildCaseWhenUpdateSql(batch, userTableName, fieldName);
    }

    /**
     * 构建CASE WHEN批量更新SQL
     *
     * @param batch         批量记录
     * @param userTableName 用户表名
     * @param fieldName     字段名
     * @return CASE WHEN更新SQL
     * <AUTHOR>
     */
    private String buildCaseWhenUpdateSql(List<UpdateRecord> batch, String userTableName, String fieldName) {
        StringBuilder sqlBuilder = new StringBuilder();
        StringBuilder oneIdList = new StringBuilder();
        
        // 构建UPDATE语句开头
        sqlBuilder.append("UPDATE ").append(userTableName).append(" SET ").append(fieldName).append(" = CASE oneId ");
        
        // 构建CASE WHEN子句和oneId列表
        for (int i = 0; i < batch.size(); i++) {
            UpdateRecord record = batch.get(i);
            
            // CASE WHEN子句
            sqlBuilder.append("WHEN '").append(record.getOneId()).append("' THEN ").append(record.getArrayValue()).append(" ");
            
            // oneId列表
            if (i > 0) {
                oneIdList.append(", ");
            }
            oneIdList.append("'").append(record.getOneId()).append("'");
        }
        
        // 完成SQL语句
        sqlBuilder.append("END WHERE oneId IN (").append(oneIdList).append(")");
        
        return sqlBuilder.toString();
    }

    /**
     * 处理LLM标签数据（优化版本：批量处理）
     *
     * @param tenantId    租户ID
     * @param label       标签信息
     * @param sessionData 会话数据
     * @param labelKey    标签key（如"微信"）
     * <AUTHOR>
     */
    private void processLLMTagData(String tenantId, LabelWithBLOBs label, List<Map<String, Object>> sessionData, String labelKey) {
        log.info("开始处理LLM标签数据，数据量: {}", sessionData.size());

        String userTableName = TenantUtils.generateMockUserTableName(tenantId);
        if (label.getField() == null) {
            log.error("标签字段ID为空，无法处理LLM标签数据: labelId={}", label.getId());
            return;
        }
        String fieldName = SqlConstants.StringConstants.LABEL_FIELD_PREFIX + label.getField();

        // 批量处理数据，避免单条更新的性能问题
        processBatchLLMTagData(sessionData, userTableName, fieldName, labelKey);
        
        log.info("LLM标签数据处理完成");
    }

    /**
     * 批量处理LLM标签数据
     *
     * @param sessionData   会话数据
     * @param userTableName 用户表名
     * @param fieldName     字段名
     * @param labelKey      标签key
     * <AUTHOR>
     */
    private void processBatchLLMTagData(List<Map<String, Object>> sessionData, String userTableName, 
                                       String fieldName, String labelKey) {
        // 预处理数据，提取有效的更新记录
        List<UpdateRecord> updateRecords = new ArrayList<>();
        
        for (Map<String, Object> row : sessionData) {
            String oneId = (String) row.get(SqlConstants.FieldNames.ONE_ID);
            Object tagExtractInfoObj = row.get(SqlConstants.FieldNames.TAG_EXTRACT_INFO);

            if (StringUtils.isBlank(oneId) || tagExtractInfoObj == null) {
                continue;
            }

            try {
                // 安全地获取JSON字符串
                String tagExtractInfoJson = safeGetJsonString(tagExtractInfoObj);
                if (StringUtils.isBlank(tagExtractInfoJson)) {
                    continue;
                }

                // 解析tagExtractInfo JSON数组
                JsonNode tagExtractInfo = objectMapper.readTree(tagExtractInfoJson);
                String tagValue = findTagValueByKey(tagExtractInfo, labelKey);

                if (StringUtils.isNotBlank(tagValue)) {
                    // 构建Doris单值数组格式
                    String escapedValue = buildDorisSingleArrayValue(tagValue);
                    updateRecords.add(new UpdateRecord(oneId, escapedValue));
                }
            } catch (Exception e) {
                log.error("解析用户 {} 的LLM标签数据失败，跳过该用户: {}", oneId, tagExtractInfoObj, e);
            }
        }

        log.info("LLM标签预处理完成，有效更新记录数: {}", updateRecords.size());

        // 分批执行更新
        executeBatchUpdate(updateRecords, userTableName, fieldName);
    }

    /**
     * 处理风险评估数据（优化版本：批量处理）
     *
     * @param tenantId    租户ID
     * @param label       标签信息
     * @param timeData    时间数据
     * @param contentData 对话内容数据
     * <AUTHOR>
     */
    private void processRiskAssessmentData(String tenantId, LabelWithBLOBs label,
                                           List<Map<String, Object>> timeData,
                                           List<Map<String, Object>> contentData) {
        log.info("开始处理风险评估数据，时间数据量: {}, 内容数据量: {}", timeData.size(), contentData.size());

        String userTableName = TenantUtils.generateMockUserTableName(tenantId);
        if (label.getField() == null) {
            log.error("标签字段ID为空，无法处理风险评估数据: labelId={}", label.getId());
            return;
        }
        String fieldName = SqlConstants.StringConstants.LABEL_FIELD_PREFIX + label.getField();
        LocalDate today = LocalDate.now();

        // 构建时间风险映射
        Map<String, String> timeRiskMap = buildTimeRiskMap(timeData, today);

        // 构建内容风险映射
        Map<String, String> contentRiskMap = buildContentRiskMap(contentData);

        // 获取所有需要处理的用户ID
        Set<String> allUserIds = new HashSet<>();
        allUserIds.addAll(timeRiskMap.keySet());
        allUserIds.addAll(contentRiskMap.keySet());

        log.info("需要处理的用户总数: {}", allUserIds.size());

        // 批量处理风险评估数据
        processBatchRiskAssessmentData(allUserIds, timeRiskMap, contentRiskMap, userTableName, fieldName);
        
        log.info("风险评估数据处理完成");
    }

    /**
     * 批量处理风险评估数据
     *
     * @param allUserIds    所有用户ID
     * @param timeRiskMap   时间风险映射
     * @param contentRiskMap 内容风险映射
     * @param userTableName 用户表名
     * @param fieldName     字段名
     * <AUTHOR>
     */
    private void processBatchRiskAssessmentData(Set<String> allUserIds, Map<String, String> timeRiskMap,
                                               Map<String, String> contentRiskMap, String userTableName, 
                                               String fieldName) {
        
        // 预处理数据，提取有效的更新记录
        List<UpdateRecord> updateRecords = new ArrayList<>();
        
        for (String oneId : allUserIds) {
            try {
                // 获取时间风险
                String timeRisk = timeRiskMap.get(oneId);

                // 获取内容风险
                String contentRisk = contentRiskMap.get(oneId);

                // 取较大的风险等级作为最终风险
                String finalRisk = getHigherRisk(timeRisk, contentRisk);

                // 构建Doris单值数组格式
                String escapedRisk = buildDorisSingleArrayValue(finalRisk);
                updateRecords.add(new UpdateRecord(oneId, escapedRisk));

            } catch (Exception e) {
                log.error("处理用户 {} 的风险评估数据失败，跳过该用户", oneId, e);
            }
        }

        log.info("风险评估数据预处理完成，有效更新记录数: {}", updateRecords.size());

        // 分批执行更新
        executeBatchUpdate(updateRecords, userTableName, fieldName);
    }

    /**
     * 计算时间风险
     * 根据最后通话时间距今天数计算风险等级
     *
     * @param lastCallDateObj 最后通话时间（可能是LocalDateTime或String）
     * @param today           今天日期
     * @return 风险等级
     * <AUTHOR>
     */
    private String calculateTimeRisk(Object lastCallDateObj, LocalDate today) {
        if (lastCallDateObj == null) {
            return RiskAssessmentConstants.RiskLevel.HIGH_RISK; // 没有通话记录，高风险
        }

        try {
            LocalDate lastCallDate;

            // 处理不同的时间类型
            if (lastCallDateObj instanceof LocalDateTime) {
                lastCallDate = ((LocalDateTime) lastCallDateObj).toLocalDate();
            } else if (lastCallDateObj instanceof String lastCallDateStr) {
                if (StringUtils.isBlank(lastCallDateStr)) {
                    return RiskAssessmentConstants.RiskLevel.HIGH_RISK;
                }
                // 尝试多种日期格式
                lastCallDate = LocalDateTimeUtils.parseFlexibleDate(lastCallDateStr);
            } else {
                log.warn("不支持的时间类型: {}", lastCallDateObj.getClass().getName());
                return RiskAssessmentConstants.RiskLevel.HIGH_RISK;
            }

            long daysBetween = ChronoUnit.DAYS.between(lastCallDate, today);

            if (daysBetween <= RiskAssessmentConstants.TimeRiskThreshold.LOW_RISK_DAYS) {
                return RiskAssessmentConstants.RiskLevel.HIGH_RISK;
            } else if (daysBetween <= RiskAssessmentConstants.TimeRiskThreshold.MEDIUM_RISK_DAYS) {
                return RiskAssessmentConstants.RiskLevel.MEDIUM_RISK;
            } else {
                return RiskAssessmentConstants.RiskLevel.LOW_RISK;
            }
        } catch (Exception e) {
            log.error("计算时间风险时发生未知错误: {}", lastCallDateObj, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "计算时间风险时发生未知错误");
        }
    }


    /**
     * 计算LLM风险
     * 调用大模型服务进行风险评估
     *
     * @param conversationContent 对话内容
     * @return 风险等级
     * <AUTHOR>
     */
    private String calculateLLMRisk(String conversationContent) {
        try {
            // 调用大模型风险评估服务
            return callLLMRiskAssessmentService(conversationContent);
        } catch (Exception e) {
            // 其他异常，记录日志但不中断整个流程
            log.error("调用LLM风险评估服务失败，使用默认风险等级", e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "调用LLM风险评估服务失败");
        }
    }

    /**
     * 调用大模型风险评估服务
     * 使用真实的LLM服务进行风险评估
     *
     * @param conversationContent 对话内容
     * @return 风险等级
     * <AUTHOR>
     */
    private String callLLMRiskAssessmentService(String conversationContent) {
        log.debug("调用LLM风险评估服务，对话内容长度: {}", conversationContent.length());

        try {
            // 调用真实的LLM风险评估服务
            String riskLevel = llmRiskAssessmentService.assessRisk(conversationContent);
            log.debug("LLM风险评估结果: {}", riskLevel);
            return riskLevel;
        } catch (Exception e) {
            log.error("调用LLM风险评估服务失败，对话内容长度: {}", conversationContent.length(), e);
            // 重新抛出异常，让上层方法处理
            throw e;
        }
    }


    /**
     * 获取较高的风险等级
     * 使用枚举优化，提供类型安全的风险等级比较
     *
     * @param risk1 风险1
     * @param risk2 风险2
     * @return 较高的风险等级
     * <AUTHOR>
     */
    private String getHigherRisk(String risk1, String risk2) {
        return RiskAssessmentConstants.RiskLevelEnum.getHigherRisk(risk1, risk2);
    }

    /**
     * 构建时间风险映射
     *
     * @param timeData 时间数据
     * @param today    今天日期
     * @return 用户ID -> 时间风险等级的映射
     * <AUTHOR>
     */
    private Map<String, String> buildTimeRiskMap(List<Map<String, Object>> timeData, LocalDate today) {
        Map<String, String> timeRiskMap = new HashMap<>();

        for (Map<String, Object> row : timeData) {
            String oneId = (String) row.get(SqlConstants.FieldNames.ONE_ID);
            Object lastCallDateObj = row.get(SqlConstants.FieldNames.LAST_CALL_DATE);

            if (StringUtils.isBlank(oneId) || lastCallDateObj == null) {
                continue;
            }

            String timeRisk = calculateTimeRisk(lastCallDateObj, today);
            timeRiskMap.put(oneId, timeRisk);
        }

        log.info("构建时间风险映射完成，用户数: {}", timeRiskMap.size());
        return timeRiskMap;
    }

    /**
     * 构建内容风险映射
     * SQL已经限制了每个用户最多5条记录，这里直接按用户分组处理
     *
     * @param contentData 对话内容数据（已按用户限制为最多5条）
     * @return 用户ID -> 内容风险等级的映射
     * <AUTHOR>
     */
    private Map<String, String> buildContentRiskMap(List<Map<String, Object>> contentData) {
        Map<String, String> contentRiskMap = new HashMap<>();
        Map<String, List<String>> userConversations = new HashMap<>();

        // 按用户分组对话内容（SQL已经限制每个用户最多5条）
        for (Map<String, Object> row : contentData) {
            String oneId = (String) row.get(SqlConstants.FieldNames.ONE_ID);
            String conversationContent = (String) row.get(SqlConstants.FieldNames.CONVERSATION_CONTENT);

            if (StringUtils.isBlank(oneId) || StringUtils.isBlank(conversationContent)) {
                continue;
            }

            userConversations.computeIfAbsent(oneId, k -> new ArrayList<>()).add(conversationContent);
        }

        // 并发为每个用户分析风险
        log.info("开始并发分析 {} 个用户的对话内容风险", userConversations.size());
        long llmStartTime = System.currentTimeMillis();

        List<CompletableFuture<Void>> futures = userConversations.entrySet().stream()
            .map(entry -> CompletableFuture.runAsync(() -> {
                String oneId = entry.getKey();
                List<String> conversations = entry.getValue();

                try {
                    // 将多条对话合并后进行LLM分析
                    String combinedContent = String.join(SqlConstants.StringConstants.SPACE_SEPARATOR, conversations);
                    String contentRisk = calculateLLMRisk(combinedContent);

                    // 线程安全地更新结果
                    synchronized (contentRiskMap) {
                        contentRiskMap.put(oneId, contentRisk);
                    }
                } catch (Exception e) {
                    log.error("分析用户 {} 的对话内容风险失败", oneId, e);
                    // 分析失败时设为低风险
                    synchronized (contentRiskMap) {
                        contentRiskMap.put(oneId, RiskAssessmentConstants.RiskLevel.LOW_RISK);
                    }
                }
            }, llmExecutorService))
            .toList();

        // 等待所有LLM调用完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        long llmEndTime = System.currentTimeMillis();
        long llmDuration = llmEndTime - llmStartTime;

        log.info("并发LLM风险分析完成，实际处理用户数: {}, 总耗时: {}ms", contentRiskMap.size(), llmDuration);

        log.info("构建内容风险映射完成，用户数: {}", contentRiskMap.size());
        return contentRiskMap;
    }

    /**
     * 构建Doris数组格式的值
     *
     * @param values 值列表
     * @return Doris数组格式字符串，如：['value1', 'value2']
     * <AUTHOR>
     */
    private String buildDorisArrayValue(List<String> values) {
        if (CollectionUtils.isEmpty(values)) {
            return SqlConstants.StringConstants.EMPTY_ARRAY;
        }

        // 转义单引号并构建数组格式
        List<String> escapedValues = values.stream()
                .map(value -> "'" + value.replace("'", SqlConstants.StringConstants.SINGLE_QUOTE_ESCAPE) + "'")
                .collect(Collectors.toList());

        return "[" + String.join(SqlConstants.StringConstants.COMMA_SEPARATOR, escapedValues) + "]";
    }

    /**
     * 构建Doris单元素数组的SQL格式
     * 用于标签字段（数组类型），将单个值包装成数组格式
     *
     * @param value 单个值
     * @return 转义后的数组格式字符串，如 ['低风险'] 或 NULL
     * <AUTHOR>
     */
    private String buildDorisSingleArrayValue(String value) {
        if (StringUtils.isBlank(value)) {
            return SqlConstants.StringConstants.NULL_STRING;
        }
        // 将单个值包装成数组格式：['value']
        String escapedValue = value.replace("'", SqlConstants.StringConstants.SINGLE_QUOTE_ESCAPE);
        return "['" + escapedValue + "']";
    }

    /**
     * 安全地获取JSON字符串
     * 处理不同类型的JSON对象，避免类型转换异常
     *
     * @param jsonObj JSON对象（可能是String、JsonNode或其他类型）
     * @return JSON字符串
     * <AUTHOR>
     */
    private String safeGetJsonString(Object jsonObj) {
        if (jsonObj == null) {
            return null;
        }

        try {
            if (jsonObj instanceof String) {
                return (String) jsonObj;
            } else if (jsonObj instanceof JsonNode) {
                return objectMapper.writeValueAsString(jsonObj);
            } else {
                // 对于其他类型，尝试转换为字符串
                return String.valueOf(jsonObj);
            }
        } catch (Exception e) {
            log.warn("转换JSON对象为字符串失败: {}", jsonObj, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "JSON对象转换失败");
        }
    }

    /**
     * 从tagExtractInfo数组中根据key查找对应的value
     * tagExtractInfo格式：[{"key": "产品兴趣", "value": "[感兴趣]"}, ...]
     *
     * @param tagExtractInfo JSON数组节点
     * @param targetKey      要查找的key（如"产品兴趣"）
     * @return 对应的value值（已去除内置方括号），如果未找到返回null
     * <AUTHOR>
     */
    private String findTagValueByKey(JsonNode tagExtractInfo, String targetKey) {
        if (tagExtractInfo == null || StringUtils.isBlank(targetKey)) {
            return null;
        }

        try {
            // 检查是否为数组
            if (tagExtractInfo.isArray()) {
                for (JsonNode item : tagExtractInfo) {
                    if (item.has(SqlConstants.FieldNames.JSON_KEY) && item.has(SqlConstants.FieldNames.JSON_VALUE)) {
                        String key = item.get(SqlConstants.FieldNames.JSON_KEY).asText();
                        if (targetKey.equals(key)) {
                            String rawValue = item.get(SqlConstants.FieldNames.JSON_VALUE).asText();
                            // 去除value中的内置方括号
                            return StringUtil.removeInnerBrackets(rawValue);
                        }
                    }
                }
            } else {
                log.warn("tagExtractInfo不是数组格式: {}", tagExtractInfo);
            }
        } catch (Exception e) {
            log.error("解析tagExtractInfo时发生未知错误: {}", tagExtractInfo, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "解析tagExtractInfo时发生未知错误");
        }
        return null;
    }


    /**
     * 通过catalogId获取robotName
     * 三级目录名称就是robotName
     * @param tenantId 租户ID
     * @param catalogId 目录ID
     * @return robotName，如果获取失败返回null
     * <AUTHOR>
     */
    private String getRobotNameByCatalogId(String tenantId, Long catalogId) {
        if (catalogId == null) {
            log.warn("catalogId为空，无法获取robotName");
            return null;
        }

        try {
            LabelCatalog catalog = labelCatalogService.getCatalogDetail(catalogId, tenantId);
            if (catalog == null) {
                log.warn("找不到catalogId为 {} 的目录", catalogId);
                return null;
            }

            // 三级目录名称就是robotName
            String robotName = catalog.getCatalogName();
            log.debug("通过catalogId {} 获取到robotName: {}", catalogId, robotName);
            return robotName;

        } catch (Exception e) {
            log.error("通过catalogId {} 获取robotName失败", catalogId, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "通过catalogId获取robotName失败");
        }
    }

    /**
     * 计算内置标签的distribution统计
     * 直接复用LabelCalculateServiceImpl中已验证的逻辑和SQL
     *
     * @param tenantId 租户ID
     * @param label 标签信息
     * @return distribution JSON字符串
     * <AUTHOR>
     */
    private String calculateBuiltinLabelDistribution(String tenantId, LabelWithBLOBs label) {
        try {
            if (label.getField() == null) {
                log.warn("标签字段ID为空，无法计算distribution: labelId={}", label.getId());
                return null;
            }

            String userTableName = TenantUtils.generateMockUserTableName(tenantId);
            String fieldName = SqlConstants.StringConstants.LABEL_FIELD_PREFIX + label.getField();

            // 使用工具类计算distribution
            LabelDistribute labelDistribute = labelDistributionCalculator.calculateBuiltinLabelDistribution(userTableName, fieldName);

            // 转换为JSON字符串
            return JsonUtils.toJsonWithOutException(labelDistribute);

        } catch (Exception e) {
            log.error("计算内置标签distribution失败: labelId={}, tenantId={}", label.getId(), tenantId, e);
            return null;
        }
    }


}
