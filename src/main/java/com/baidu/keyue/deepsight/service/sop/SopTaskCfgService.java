package com.baidu.keyue.deepsight.service.sop;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisCompletionTimeResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskCfgRequestBase;
import com.baidu.keyue.deepsight.models.sop.SopTaskCreateRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskQueryRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskUpdateRequest;

import java.util.List;

/**
 * SOP离线分析任务配置服务接口
 */
public interface SopTaskCfgService {

    /**
     * 创建离线分析任务
     * @param tenantId 租户ID
     * @param request 创建请求
     * @param creator 创建者
     * @return 任务ID
     */
    Long createOfflineTask(String tenantId, SopTaskCreateRequest request, String creator);


    SopAnalysisCompletionTimeResponse getTaskCompletionTime(String tenantId, SopTaskCfgRequestBase request);

    /**
     * 分页查询离线分析任务
     *
     * @param tenantId 租户ID
     * @param request  查询请求
     * @return 分页结果
     */
    BasePageResponse.Page<SopTaskResponse> queryOfflineTasks(String tenantId, SopTaskQueryRequest request);

    /**
     * 根据ID查询离线分析任务
     * @param tenantId 租户ID
     * @param taskId 任务ID
     * @return 任务详情
     */
    SopTaskResponse getOfflineTaskById(String tenantId, Long taskId);

    /**
     * 更新任务状态
     * @param tenantId 租户ID
     * @param taskId 任务ID
     * @param status 状态
     * @param modifier 修改者
     */
    void updateTaskStatus(String tenantId, Long taskId, Byte status, String modifier);

    /**
     * 更新任务执行信息
     * @param tenantId 租户ID
     * @param taskId 任务ID
     * @param taskMsg 任务执行信息
     */
    void updateTaskMessage(String tenantId, Long taskId, String taskMsg);

    /**
     * 编辑离线分析任务
     * @param tenantId 租户ID
     * @param request 编辑请求
     * @param modifier 修改者
     */
    void updateOfflineTask(String tenantId, SopTaskUpdateRequest request, String modifier);

    /**
     * 删除离线分析任务（支持取消运行中的任务）
     * @param tenantId 租户ID
     * @param taskId 任务ID
     */
    void deleteOfflineTask(String tenantId, Long taskId);

    /**
     * 取消正在运行的离线分析任务
     * @param tenantId 租户ID
     * @param taskId 任务ID
     * @param operator 操作者
     * @return 是否成功取消
     */
    boolean cancelRunningTask(String tenantId, Long taskId, String operator);

    /**
     * 检查是否存在相同配置的任务
     *
     * @param id           当前任务ID，用于排除自身
     * @param userConfigId 用户配置ID
     * @param tenantId     租户ID
     * @param requestBase  任务配置请求基础信息
     * @return 是否存在
     */
    boolean existsSameTask(Long id, Long userConfigId, String tenantId, SopTaskCfgRequestBase requestBase);

    /**
     * 获取租户下指定状态的任务列表
     * @param tenantId 租户ID，为null时查询所有租户
     * @param status 状态，为null时查询所有状态
     * @return 任务列表
     */
    List<SopTaskResponse> getTasksByStatus(String tenantId, Byte status);
}