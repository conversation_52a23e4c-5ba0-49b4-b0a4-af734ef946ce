package com.baidu.keyue.deepsight.service.builtin;

import com.baidu.keyue.deepsight.enums.DataTableStatusEnum;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.PresetEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.label.UserMetricFieldInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 用户指标表元数据初始化服务
 * 负责为 user_metric 表创建元数据记录，以支持内置标签的规则解析
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@Service
public class UserMetricTableInitService {

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;
    
    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    /**
     * 为租户初始化 user_metric 表的元数据
     * 如果已存在则跳过
     * 
     * @param tenantId 租户ID
     * @return user_metric 表的 dataTableId
     */
    @Transactional
    public Long initUserMetricTableMetadata(String tenantId) {
        log.info("开始为租户 {} 初始化 user_metric 表元数据", tenantId);
        
        String userMetricTableName = TenantUtils.generateUserMetricTableName(tenantId);
        
        // 检查表和字段元数据是否完整
        DataTableInfo existingTable = getExistingUserMetricTable(tenantId, userMetricTableName);
        if (existingTable != null) {
            // 表存在，但需要检查字段元数据是否完整
            if (isFieldMetadataComplete(existingTable.getId(), userMetricTableName)) {
                log.info("user_metric 表元数据已完整，跳过初始化: {}", userMetricTableName);
                return existingTable.getId();
            } else {
                log.warn("user_metric 表存在但字段元数据不完整，补充字段元数据: {}", userMetricTableName);
                // 补充缺失的字段元数据
                createUserMetricFieldsMetadata(existingTable.getId(), userMetricTableName);
                log.info("user_metric 表字段元数据补充完成: {}", userMetricTableName);
                return existingTable.getId();
            }
        }
        
        // 创建表元数据
        DataTableInfo tableInfo = createUserMetricTableInfo(tenantId, userMetricTableName);
        dataTableInfoMapper.insert(tableInfo);
        log.info("创建 user_metric 表元数据成功: {}, ID: {}", userMetricTableName, tableInfo.getId());
        
        // 创建字段元数据
        createUserMetricFieldsMetadata(tableInfo.getId(), userMetricTableName);
        
        log.info("user_metric 表元数据初始化完成: {}", userMetricTableName);
        return tableInfo.getId();
    }

    /**
     * 检查字段元数据是否完整
     * 检查关键字段是否都存在
     */
    private boolean isFieldMetadataComplete(Long dataTableId, String tableName) {
        try {
            // 检查关键字段是否都存在
            String[] requiredFields = {"oneId", "connect_rate", "first_round_hangup_rate", "avg_rounds"};

            for (String fieldName : requiredFields) {
                TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
                criteria.createCriteria()
                        .andDataTableIdEqualTo(dataTableId)
                        .andEnFieldEqualTo(fieldName)
                        .andIsVisableEqualTo(true);

                List<TableFieldMetaInfo> fields = tableFieldMetaInfoMapper.selectByExample(criteria);
                if (CollectionUtils.isEmpty(fields)) {
                    log.debug("字段元数据不完整，缺少字段: {}.{}", tableName, fieldName);
                    return false;
                }
            }

            log.debug("字段元数据完整: {}", tableName);
            return true;

        } catch (Exception e) {
            log.warn("检查字段元数据完整性失败: {}", tableName, e);
            return false;
        }
    }

    /**
     * 获取已存在的 user_metric 表信息
     */
    private DataTableInfo getExistingUserMetricTable(String tenantId, String tableName) {
        DataTableInfoCriteria criteria = new DataTableInfoCriteria();
        criteria.createCriteria()
                .andTenantidEqualTo(tenantId)
                .andTableNameEqualTo(tableName)
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode());
        
        List<DataTableInfo> tables = dataTableInfoMapper.selectByExample(criteria);
        return CollectionUtils.isEmpty(tables) ? null : tables.get(0);
    }

    /**
     * 创建 user_metric 表信息
     */
    private DataTableInfo createUserMetricTableInfo(String tenantId, String tableName) {
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setEnName("user_metric");
        tableInfo.setTenantid(tenantId);
        tableInfo.setTableName(tableName);
        tableInfo.setCnName("用户指标表");
        tableInfo.setDescription("用户外呼行为指标聚合表，用于内置标签计算");
        tableInfo.setIsVisable(true);
        tableInfo.setIsDel(DelEnum.NOT_DELETED.getCode());
        tableInfo.setDataType(0);
        tableInfo.setStatus(DataTableStatusEnum.CREATED.getStatus().byteValue());
        tableInfo.setIsPreset(PresetEnum.PRESET.getCode());
        tableInfo.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
        tableInfo.setCreateTime(new Date());
        tableInfo.setUpdateTime(new Date());
        return tableInfo;
    }

    /**
     * 创建 user_metric 表的字段元数据
     */
    private void createUserMetricFieldsMetadata(Long dataTableId, String tableName) {
        // 定义字段信息（与 Doris 表结构保持一致）
        List<UserMetricFieldInfo> fieldInfos = Arrays.asList(
            new UserMetricFieldInfo("oneId", "用户ID", "VARCHAR", TableFieldTagEnum.PRIMARY),
            new UserMetricFieldInfo("connect_rate", "接通率", "DOUBLE", TableFieldTagEnum.MEASURE),
            new UserMetricFieldInfo("time_bucket_statistics", "时间段接通统计", "JSON", TableFieldTagEnum.NULL),
            new UserMetricFieldInfo("first_round_hangup_rate", "首轮挂断率", "DOUBLE", TableFieldTagEnum.MEASURE),
            new UserMetricFieldInfo("avg_rounds", "平均对话轮次", "DOUBLE", TableFieldTagEnum.MEASURE),
            new UserMetricFieldInfo("avg_duration", "平均通话时长", "DOUBLE", TableFieldTagEnum.MEASURE),
            new UserMetricFieldInfo("last_call_date", "最后呼叫日期", "DATE", TableFieldTagEnum.NULL),
            new UserMetricFieldInfo("weekly_hourly_auto_answer_calls", "每周每小时小秘书接通数", "JSON", TableFieldTagEnum.NULL),
            new UserMetricFieldInfo("weekly_hourly_lost_calls_rate", "每周每小时未接通率", "JSON", TableFieldTagEnum.NULL)
        );

        // 批量创建字段元数据（增量添加，避免重复）
        int createdCount = 0;
        int skippedCount = 0;

        for (UserMetricFieldInfo fieldInfo : fieldInfos) {
            // 检查字段是否已存在
            if (isFieldExists(dataTableId, fieldInfo.getFieldName())) {
                log.debug("字段已存在，跳过: {}.{}", tableName, fieldInfo.getFieldName());
                skippedCount++;
                continue;
            }

            // 创建字段元数据
            TableFieldMetaInfo metaInfo = createFieldMetaInfo(dataTableId, tableName, fieldInfo);
            tableFieldMetaInfoMapper.insert(metaInfo);
            log.debug("创建字段元数据: {}.{}", tableName, fieldInfo.getFieldName());
            createdCount++;
        }

        log.info("user_metric 表字段元数据处理完成，总数: {}, 新建: {}, 跳过: {}",
                fieldInfos.size(), createdCount, skippedCount);
    }

    /**
     * 检查字段是否已存在
     */
    private boolean isFieldExists(Long dataTableId, String fieldName) {
        try {
            TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
            criteria.createCriteria()
                    .andDataTableIdEqualTo(dataTableId)
                    .andEnFieldEqualTo(fieldName);

            List<TableFieldMetaInfo> fields = tableFieldMetaInfoMapper.selectByExample(criteria);
            return !CollectionUtils.isEmpty(fields);

        } catch (Exception e) {
            log.warn("检查字段是否存在失败: dataTableId={}, fieldName={}", dataTableId, fieldName, e);
            return false;
        }
    }

    /**
     * 创建单个字段的元数据信息
     */
    private TableFieldMetaInfo createFieldMetaInfo(Long dataTableId, String tableName, UserMetricFieldInfo fieldInfo) {
        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setDataTableId(dataTableId);
        metaInfo.setTableEnName(tableName);
        metaInfo.setEnField(fieldInfo.getFieldName());
        metaInfo.setCnField(fieldInfo.getFieldDesc());
        metaInfo.setDataType(fieldInfo.getDataType());
        metaInfo.setFieldTag(fieldInfo.getFieldTag().getCode());
        metaInfo.setFieldType(fieldInfo.getDataType());
        metaInfo.setIsFilterCriteria(false);
        metaInfo.setIsRequired(false);
        metaInfo.setIsVisable(true);
        metaInfo.setIsSecrete(false);
        metaInfo.setCreateTime(new Date());
        metaInfo.setUpdateTime(new Date());
        
        // 设置取值类型
        if ("DOUBLE".equals(fieldInfo.getDataType())) {
            metaInfo.setValueType("number");
        } else if ("JSON".equals(fieldInfo.getDataType())) {
            metaInfo.setValueType("text");
        } else if ("DATE".equals(fieldInfo.getDataType())) {
            metaInfo.setValueType("time");
        } else {
            metaInfo.setValueType("text");
        }
        
        return metaInfo;
    }

    /**
     * 获取 user_metric 表的 dataTableId
     *
     * @param tenantId 租户ID
     * @return 表ID
     */
    public Long getUserMetricTableId(String tenantId) {
        String userMetricTableName = TenantUtils.generateUserMetricTableName(tenantId);
        DataTableInfo tableInfo = getExistingUserMetricTable(tenantId, userMetricTableName);

        if (tableInfo == null) {
            throw new RuntimeException("user_metric 表不存在: " + userMetricTableName);
        }

        return tableInfo.getId();
    }

    /**
     * 获取 user_metric 表中指定字段的ID
     *
     * @param tenantId 租户ID
     * @param fieldName 字段名
     * @return 字段ID
     */
    public Long getUserMetricFieldId(String tenantId, String fieldName) {
        String userMetricTableName = TenantUtils.generateUserMetricTableName(tenantId);
        
        TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
        criteria.createCriteria()
                .andTableEnNameEqualTo(userMetricTableName)
                .andEnFieldEqualTo(fieldName)
                .andIsVisableEqualTo(true);
        
        List<TableFieldMetaInfo> fields = tableFieldMetaInfoMapper.selectByExample(criteria);
        if (CollectionUtils.isEmpty(fields)) {
            // 如果字段不存在，尝试初始化表元数据
            log.warn("字段不存在，尝试初始化 user_metric 表元数据: {}.{}", userMetricTableName, fieldName);
            initUserMetricTableMetadata(tenantId);
            
            // 重新查询
            fields = tableFieldMetaInfoMapper.selectByExample(criteria);
            if (CollectionUtils.isEmpty(fields)) {
                throw new RuntimeException("字段不存在: " + userMetricTableName + "." + fieldName);
            }
        }
        
        return fields.get(0).getId();
    }

}
