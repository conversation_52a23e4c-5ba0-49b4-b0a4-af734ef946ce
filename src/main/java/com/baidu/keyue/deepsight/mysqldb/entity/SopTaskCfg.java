package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * SOP离线分析任务配置实体类
 * This class corresponds to the database table sop_task_cfg
 *
 * <AUTHOR>
 */
public class SopTaskCfg implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 机器人ID
     */
    private String robotId;

    /**
     * 机器人场景：5-快捷场景，6-灵活画布
     */
    private Byte robotScene;

    /**
     * 机器人版本
     */
    private String robotVersion;

    /**
     * 是否自动应答：0-否，1-是
     */
    private Byte isAutoAnswer;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 标签
     */
    private String tags;

    /**
     * 任务状态：1-执行中，2-计算失败，3-成功，4-已取消
     */
    private Byte status;

    /**
     * 任务执行信息描述
     */
    private String taskMsg;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 机器人名称
     */
    private String robotName;

    /**
     * 指标配置，一个任务一个指标配置
     */
    private Long userConfigId;

    /**
     * 机器人版本名称列表，跟版本一一对应
     */
    private String robotVersionName;

    /**
     * 机器人图标
     */
    private String robotIcon;

    /**
     * 分析通话数量
     */
    private Integer analysisCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId == null ? null : taskId.trim();
    }

    public String getRobotId() {
        return robotId;
    }

    public void setRobotId(String robotId) {
        this.robotId = robotId == null ? null : robotId.trim();
    }

    public Byte getRobotScene() {
        return robotScene;
    }

    public void setRobotScene(Byte robotScene) {
        this.robotScene = robotScene;
    }

    public String getRobotVersion() {
        return robotVersion;
    }

    public void setRobotVersion(String robotVersion) {
        this.robotVersion = robotVersion == null ? null : robotVersion.trim();
    }

    public Byte getIsAutoAnswer() {
        return isAutoAnswer;
    }

    public void setIsAutoAnswer(Byte isAutoAnswer) {
        this.isAutoAnswer = isAutoAnswer;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags == null ? null : tags.trim();
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getTaskMsg() {
        return taskMsg;
    }

    public void setTaskMsg(String taskMsg) {
        this.taskMsg = taskMsg == null ? null : taskMsg.trim();
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getRobotName() {
        return robotName;
    }

    public void setRobotName(String robotName) {
        this.robotName = robotName;
    }

    public Long getUserConfigId() {
        return userConfigId;
    }

    public void setUserConfigId(Long userConfigId) {
        this.userConfigId = userConfigId;
    }

    public String getRobotVersionName() {
        return robotVersionName;
    }

    public void setRobotVersionName(String robotVersionName) {
        this.robotVersionName = robotVersionName == null ? null : robotVersionName.trim();
    }

    public String getRobotIcon() {
        return robotIcon;
    }

    public void setRobotIcon(String robotIcon) {
        this.robotIcon = robotIcon == null ? null : robotIcon.trim();
    }

    public Integer getAnalysisCount() {
        return analysisCount;
    }

    public void setAnalysisCount(Integer analysisCount) {
        this.analysisCount = analysisCount;
    }
}