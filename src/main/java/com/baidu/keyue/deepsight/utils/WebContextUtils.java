package com.baidu.keyue.deepsight.utils;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.constants.BuiltinLabelConstants;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import lombok.experimental.UtilityClass;

import java.util.Optional;

/**
 * Web上下文工具类
 * 提供优雅的上下文操作方法
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@UtilityClass
public class WebContextUtils {

    /**
     * 创建或更新系统用户上下文
     * 使用系统默认值填充空字段
     * 
     * @param tenantId 租户ID
     * @return 系统用户上下文
     * <AUTHOR>
     */
    public static DeepSightWebContext createOrUpdateSystemContext(String tenantId) {
        return createOrUpdateContext(tenantId, 
                BuiltinLabelConstants.SystemUser.SYSTEM_USER_ID, 
                Constants.SYSTEM_DEFAULT_USER_ID);
    }

    /**
     * 创建或更新用户上下文
     * 使用提供的默认值填充空字段
     * 
     * @param tenantId 租户ID
     * @param defaultUserId 默认用户ID
     * @param defaultUserName 默认用户名
     * @return 用户上下文
     * <AUTHOR>
     */
    public static DeepSightWebContext createOrUpdateContext(String tenantId, Long defaultUserId, String defaultUserName) {
        DeepSightWebContext context = Optional.ofNullable(WebContextHolder.getDeepSightWebContext())
                .orElse(new DeepSightWebContext());
        
        // 使用 Optional 风格的空值处理
        context.setTenantId(Optional.ofNullable(context.getTenantId())
                .orElse(Long.parseLong(tenantId)));
        context.setUserId(Optional.ofNullable(context.getUserId())
                .orElse(defaultUserId));
        context.setUserName(Optional.ofNullable(context.getUserName())
                .orElse(defaultUserName));
        
        return context;
    }


}
