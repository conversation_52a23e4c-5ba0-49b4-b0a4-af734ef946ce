package com.baidu.keyue.deepsight.utils;

import com.baidu.keyue.deepsight.constants.SqlConstants;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;

/**
 * @ClassName StringUtil
 * @Description 字符串工具类
 * <AUTHOR>
 * @Date 2025/3/12 2:24 PM
 */
public class StringUtil {

    public static String null2str(Object obj) {
        return obj == null ? "" : obj.toString();
    }


    /**
     * uri编码
     * @param input 入参字符串
     * @param encodeSlash 是否需要编码'/'字符
     * @return 编码后的字符串
     */
    public static String uriEncode(CharSequence input, boolean encodeSlash) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            if ((ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || (ch >= '0' && ch <= '9')
                    || ch == '_' || ch == '-' || ch == '~' || ch == '.') {
                result.append(ch);
            } else if (ch == '/') {
                result.append(encodeSlash ? "%2F" : ch);
            } else {
                try {
                    result.append(URLEncoder.encode(String.valueOf(ch), "utf-8"));
                } catch (Exception e) {
                    return null;
                }
            }
        }
        return result.toString();
    }

    /**
     * 获取SQL 模糊查询
     * @param name
     * @return
     */
    public static String getSqlLike(String name){
        return "%" + name + "%";
    }


    /**
     * 去除字符串中的内置方括号
     * 例如：将 "[学习]" 转换为 "学习"，将 "[感兴趣]" 转换为 "感兴趣"
     * 如果字符串不是以方括号包围的格式，则直接返回原字符串
     *
     * @param value 原始值
     * @return 去除方括号后的值
     * <AUTHOR>
     */
    public static String removeInnerBrackets(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        // 去除首尾空格
        String trimmedValue = value.trim();

        // 检查是否以 [ 开头并以 ] 结尾
        if (trimmedValue.startsWith(SqlConstants.StringConstants.LEFT_BRACKET) &&
                trimmedValue.endsWith(SqlConstants.StringConstants.RIGHT_BRACKET) &&
                trimmedValue.length() > 2) {
            // 去除首尾的方括号
            return trimmedValue.substring(1, trimmedValue.length() - 1).trim();
        }

        // 如果不是方括号包围的格式，直接返回原值
        return trimmedValue;
    }

}
