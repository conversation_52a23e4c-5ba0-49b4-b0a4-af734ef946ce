package com.baidu.keyue.deepsight.schedule.task;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.constants.BuiltinLabelConstants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.exception.ServiceException;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelService;
import com.baidu.keyue.deepsight.utils.WebContextUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 内置标签计算异步任务
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuiltinLabelCalculateAsyncTask {

    private final BuiltinLabelService builtinLabelService;

    /**
     * 异步处理单个租户的内置标签计算
     * 
     * @return CompletableFuture<BuiltinLabelCalculateResult> 计算结果
     * <AUTHOR>
     */
    @Async("builtinLabelCalculateTaskExecutor")
    public CompletableFuture<BuiltinLabelCalculateResult> processTenantBuiltinLabels(String tenantId) {
        log.info("开始异步处理租户 {} 的内置标签计算", tenantId);

        BuiltinLabelCalculateResult result = new BuiltinLabelCalculateResult();
        result.setTenantId(tenantId);
        result.setStartTime(System.currentTimeMillis());

        // 设置系统用户上下文，解决异步线程中ThreadLocal为空的问题
        DeepSightWebContext originalContext = WebContextHolder.getDeepSightWebContext();
        log.info("租户 {} 正在设置系统用户上下文", tenantId);
        DeepSightWebContext systemContext = WebContextUtils.createOrUpdateSystemContext(tenantId);
        WebContextHolder.setDeepSightWebContext(systemContext);
        log.info("租户 {} 系统用户上下文设置完成", tenantId);

        try {
            // 调用内置标签服务的处理逻辑
            log.info("租户 {} 开始调用内置标签服务处理逻辑", tenantId);
            builtinLabelService.ensureAndProcessBuiltinLabelsByTenant(tenantId);
            log.info("租户 {} 内置标签服务处理逻辑调用完成", tenantId);

            result.setSuccess(true);
            result.setMessage("内置标签计算成功");
            log.info("租户 {} 内置标签计算异步任务完成", tenantId);
            
        } catch (ServiceException e) {
            result.setSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setMessage("内置标签计算失败: " + e.getMessage());
            log.error("租户 {} 内置标签计算异步任务失败", tenantId, e);

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorCode(ErrorCode.INTERNAL_ERROR);
            result.setMessage("内置标签计算异常: " + e.getMessage());
            log.error("租户 {} 内置标签计算异步任务异常", tenantId, e);
        } finally {
            // 恢复原始上下文
            if (originalContext != null) {
                WebContextHolder.setDeepSightWebContext(originalContext);
            }
        }

        result.setEndTime(System.currentTimeMillis());
        result.setDuration(result.getEndTime() - result.getStartTime());

        return CompletableFuture.completedFuture(result);
    }

    /**
     * 创建系统用户Web上下文，用于异步任务执行
     *
     * @param tenantId 租户ID
     * @return 系统用户上下文
     * <AUTHOR>
     */
    private DeepSightWebContext createSystemWebContext(String tenantId) {
        UserAuthInfo authInfo = new UserAuthInfo();
        authInfo.setUserId(BuiltinLabelConstants.SystemUser.SYSTEM_USER_ID);
        authInfo.setUserName(Constants.SYSTEM_DEFAULT_USER_ID);
        authInfo.setTenantId(Long.parseLong(tenantId));

        return new DeepSightWebContext(authInfo);
    }

    /**
     * 内置标签计算结果
     * 
     * <AUTHOR>
     */
    public static class BuiltinLabelCalculateResult {
        private String tenantId;
        private boolean success;
        private String message;
        private ErrorCode errorCode;
        private long startTime;
        private long endTime;
        private long duration;

        // Getters and Setters
        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public ErrorCode getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(ErrorCode errorCode) {
            this.errorCode = errorCode;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public long getDuration() {
            return duration;
        }

        public void setDuration(long duration) {
            this.duration = duration;
        }

        @Override
        public String toString() {
            return "BuiltinLabelCalculateResult{" +
                    "tenantId='" + tenantId + '\'' +
                    ", success=" + success +
                    ", message='" + message + '\'' +
                    ", errorCode=" + errorCode +
                    ", duration=" + duration + "ms" +
                    '}';
        }
    }
}
