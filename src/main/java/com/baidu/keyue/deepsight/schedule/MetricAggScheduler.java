package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.config.AiobMetricAggConfiguration;
import com.baidu.keyue.deepsight.database.redis.RedisConfiguration;
import com.baidu.keyue.deepsight.service.agg.AiobSessionMetricAggService;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Component
public class MetricAggScheduler {

    @Value("${switch.aiobSessionMetricAgg:false}")
    private boolean aiobSessionMetricAgg;

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private AiobSessionMetricAggService aiobSessionMetricAggService;

    @Autowired
    private BuiltinLabelService builtinLabelService;

    @Autowired
    private AiobMetricAggConfiguration aiobAggConfiguration;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 统计外呼通话记录 90天内的统计指标
     * 优化：移除线程池，使用 Reactor 统一控制并发度
     *
     * <AUTHOR>
     */
    @Scheduled(cron = "${cron.pullBaiduData:0 0 2 * * *}")
    public void aiobSessionMetricAgg() {
        if (!aiobSessionMetricAgg) {
            log.info("aiobSessionMetricAgg is disabled. quit...");
            return;
        }

        log.info("aiobSessionMetricAgg task start");
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "aiobSessionMetricAgg", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("aiobSessionMetricAgg get lock failed.");
            return;
        }

        try {
            // 通过 show tables 获取aiob_conversation_session_agg_（天级别指标明细）
            List<String> sessionAggTables = aiobSessionMetricAggService.getSessionAggTables();
            if (CollectionUtils.isEmpty(sessionAggTables)) {
                log.info("aiobSessionMetricAgg got empty diffusionTask. quit...");
                return;
            }

            log.info("aiobSessionMetricAgg sessionAggTables: {}", sessionAggTables);

            // 使用 Reactor 统一控制表级别并发度，避免压垮 Doris
            Flux.fromIterable(sessionAggTables)
                .doOnNext(table -> log.info("Starting to process table: {}", table))
                .flatMap(table -> aiobSessionMetricAggService.aiobSessionMetricAggExec(table)
                    .doOnSuccess(v -> log.info("aiobSessionMetricAgg table completed: {}", table))
                    .onErrorResume(e -> {
                        log.error("aiobSessionMetricAgg table failed: {}, error:", table, e);
                        return Mono.empty(); // 单个表失败不影响其他表
                    }),
                    aiobAggConfiguration.getConcurrency().getTable()) // 限制表级别并发度
                .then()
                .doOnError(e -> log.error("aiobSessionMetricAgg task failed:", e))
                .doFinally(signalType -> log.info("aiobSessionMetricAgg task finished with signal: {}", signalType))
                .block(); // 阻塞等待完成

            // 新增：触发内置标签计算
            log.info("开始触发内置标签计算...");
            builtinLabelService.triggerBuiltinLabelCalculation();
            log.info("内置标签计算触发完成");

        } catch (Exception e) {
            log.error("try { exec task failed, ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        log.info("aiobSessionMetricAgg task finished.");
    }
}