package com.baidu.keyue.deepsight.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 内置标签配置属性类
 * 统一管理内置标签相关的配置参数
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Component
@ConfigurationProperties(prefix = "builtin-label")
public class BuiltinLabelProperties {

    /**
     * 性能相关配置
     */
    private Performance performance = new Performance();

    /**
     * 性能配置内部类
     */
    @Data
    public static class Performance {
        
        /**
         * 批量处理大小
         * 默认值：4500
         * 建议根据数据库性能和内存使用情况调整
         */
        private int batchSize = 4500;
    }
}
