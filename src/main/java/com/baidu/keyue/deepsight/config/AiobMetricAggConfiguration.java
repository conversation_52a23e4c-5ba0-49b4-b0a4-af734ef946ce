package com.baidu.keyue.deepsight.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "aiob-agg")
public class AiobMetricAggConfiguration {
    /**
     * 是否启用聚合分析
     */
    private Boolean status;
    /**
     * 统计覆盖天数，默认 90天
     */
    private Integer day;

    /**
     * 并发控制配置
     * <AUTHOR>
     */
    private Concurrency concurrency = new Concurrency();

    @Getter
    @Setter
    public static class Concurrency {
        /**
         * 表级别并发度 - 同时处理的表数量
         */
        private Integer table = 5;

        /**
         * OneId级别并发度 - 单个租户同时处理的OneId数量
         */
        private Integer oneId = 10;

        /**
         * OneId批处理大小
         */
        private Integer batchSize = 100;

        /**
         * 批次级别并发度 - 单个表同时处理的批次数量
         * <AUTHOR>
         */
        private Integer batch = 1;
    }
}
