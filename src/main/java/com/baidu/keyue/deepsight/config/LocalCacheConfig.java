package com.baidu.keyue.deepsight.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.context.annotation.Primary;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @className LocalCacheConfig
 * @description 本地缓存配置
 * @date 2025/1/8 20:34
 */
@Slf4j
@Configuration
public class LocalCacheConfig {

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("caffeineCacheManager")
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfterWrite(60, TimeUnit.SECONDS)
                // 初始的缓存空间大小
                .initialCapacity(100)
                // 缓存的最大条数
                .maximumSize(1000));
        return cacheManager;
    }

    @Bean("aiobSopCacheManager")
    public CacheManager aiobSopCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(60, TimeUnit.SECONDS)
                .maximumSize(1000));
        return cacheManager;
    }

    @Bean("aiobSopUserConfigCacheManager")
    public CacheManager aiobSopUserConfigCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(3600, TimeUnit.SECONDS)
                .maximumSize(1000));
        return cacheManager;
    }

    /**
     * LLM风险评估专用缓存管理器
     * 缓存时间：5天（定时任务每天执行，通话内容重复率高）
     * 最大缓存数：10000条记录
     *
     * @return CacheManager
     */
    @Bean("llmRiskCacheManager")
    public CacheManager llmRiskCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        cacheManager.setCaffeine(Caffeine.newBuilder()
            // 缓存5天，适合定时任务场景
            .expireAfterWrite(5, TimeUnit.DAYS)
            // 最大缓存10000条记录
            .maximumSize(10000)
            // 启用统计信息
            .recordStats());

        // 预设缓存名称
        cacheManager.setCacheNames(java.util.List.of("llmRiskCache"));

        log.info("LLM风险评估缓存管理器初始化完成 - 过期时间: 5天, 最大缓存数: 10000");

        return cacheManager;
    }

    /**
     * 缓存key生成器
     *
     * @return 缓存管理器
     */
    @Bean
    public KeyGenerator customKeyGenerator() {
        return (target, method, params) ->
                Arrays.deepHashCode(new Object[]{target.getClass().getName(), method.getName(), params});
    }

}
