package com.baidu.keyue.deepsight.config;

import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldValueEnum;
import com.baidu.keyue.deepsight.models.diffusion.dto.GradingDistributionDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @className: Constants
 * @description: 常量类
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
public class Constants {
    public static final Integer DONG_CHA_APPID = 7;

    public static final String REQUEST_ID_FIELD = "requestId";
    public static final String ERROR_CODE_PREFIX = "deepsight_";
    /**
     * 服务间调用名，来自哪个服务
     */
    public static final String SERVER_NAME = "deepsight";
    public static final String SERVER_NAME_HEADER_KEY = "X-Keyue-Server-Name";
    public static final String REQUEST_ID_HEADER_KEY = "X-Trace-Id";
    public static final String USER_ID_HEADER_KEY = "User-pk";
    public static final String USERNAME_HEADER_KEY = "Username";
    public static final String TENANT_ID_HEADER_KEY = "Agentid";

    public static final String REQUEST_USER_FIELD = "userId";
    public static final String REQUEST_TENANT_ID_FIELD = "tenantId";

    public static final String DEFAULT_USER_ID = "1234567890";
    public static final String AIOB_USER_ID = "1234567890";


    public static final long HTTP_QUERY_TIMEOUT_IN_SECONDS = 3;

    /**
     * Doris 配置
     * */
    public static final String DORIS_DEFAULT_USER_PROFILE_TABLE = "user_profile";
    public static final String DORIS_DEFAULT_LABEL_TABLE = "mock_user";
    public static final String DORIS_LABEL_FIELD_TEM = "process_label_%d";
    /** 外呼记录表 */
    public static final String DORIS_AIOB_SESSION_TABLE = "aiob_conversation_session_service";
    public static final String DORIS_MEMORY_EXTRACT_TABLE = "memory_extract_info";
    /** 外呼对话内容表 */
    public static final String DORIS_AIOB_RECORD_TABLE = "aiob_conversation_record";
    /** 客服对话内容表 */
    public static final String DORIS_KEYUE_RECORD_TABLE = "keyue_conversation_record";
    /** 访客管理数据集 */
    public static final String DORIS_VISITOR_MANAGE_TABLE = "icsoc_customer";
    public static final String DEFAULT_SESSION_TABLE_NAME = "global_default_session";
    public static final String DEFAULT_USER_TABLE_NAME = "global_default_user";
    /** id-mapping 表 */
    public static final String DORIS_ID_MAPPING_TABLE = "id_mapping";

    public static final String DORIS_CUSTOMER_GROUP_FIELD_TEM = "process_customer_%d";
    public static final String DORIS_CUSTOMER_GROUP_FIELD_PREFIX = "process_customer_";
    public static final String DORIS_CUSTOMER_GROUP_TEM_TABLE_TEM = "process_customer_temporary_t_%d";
    public static final String DORIS_CUSTOMER_GROUP_TEM_TABLE_PREFIX = "process_customer_temporary_t_";
    public static final String DORIS_ALERT_RECORD = "alert_record";


    /**
     * doris 固定字段 - 数据写入时间
     */
    public static final String DORIS_DEFAULT_DATA_INSERT_DATE_FIELD = "deepsight_datetime";
    /**
     * doris 固定字段 - 数据更新时间
     */
    public static final String DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD = "deepsight_update_datetime";

    /**
     * BSC 配置
     */
    public static final String BSC_JOB_NAME_TEM = "%s-%d";

    public static final String DORIS_LABEL_PROCESS_FIELD_NAME_PREFIX = "process_label_";
    public static final String DORIS_DATA_PREDICT_PROCESS_FIELD_NAME_PREFIX = "process_datapredict_";
    public static final String DORIS_LABEL_PROCESS_TEMPORARY_TABLE_NAME_PREFIX = "process_label_temporary_t_";
    public static final String DORIS_LABEL_PROCESS_TEMPORARY_DUP_TABLE_NAME_PREFIX = "process_label_temporary_t_dup_";

    /**
     * 字段类型
     */
    public static final String ALL_FIELDS = "ALL_FIELDS";
    public static final String REQUIRED_FIELDS = "REQUIRED_FIELDS";
    public static final String CONSTANT_NUMBER = "number";
    public static final String CONSTANT_VARCHAR = "varchar";
    public static final String CONSTANT_STRING = "string";
    public static final String CONSTANT_LIST_STRING = "list_s";
    public static final String CONSTANT_BOOLEAN = "boolean";
    public static final String CONSTANT_JSON = "json";
    public static final String CONSTANT_ARRAY = "array";


    /**
     * 租户类型
     */
    public static final String TENANT_LOGIN_TYPE = "login";
    public static final String TENANT_AIOB_TYPE = "aiob";
    public static final String TENANT_DEFAULT_TYPE = "default";
    public static final String TENANT_DEFAULT_ID = "1234567890";

    /**
     * es存储引擎
     */
    public static final String CONSTANT_CLUSTER_CODES = "cluster_codes";
    public static final int MAP_CAPACITY16 = 16;
    public static final String UPDATE_TIME = "updateTime";


    /**
     * 分隔符
     */
    public static final String SEPARATOR = ",";
    public static final String HYPHEN = "-";
    public static final String POINT = ".";
    public static final String EQUAL = "=";
    public static final String MINUTE = "`";
    public static final String SINGLE_QUOTE = "'";
    public static final String SPACE_KEY = " ";
    public static final String ENTER_KEY = "\n";

    /**
     * TODO 目前先写查询手机号
     */
    public static final String TABLE_USER_REL_FIELD = "mobile";
    public static final String TABLE_MOBILE_FIELD = "mobile";
    public static final String TABLE_USER_REL_FIELD_DATA_TYPE = "varchar";

    /**
     * doris oneId 默认字段
     * 'oneId' VARCHAR(64) NULL DEFAULT ''
     */
    public static final String TABLE_USER_ONE_ID = "oneId";
    public static final String TABLE_USER_ONE_ID_CNAME = "全局oneId";
    public static final String TABLE_USER_USER_ID = "user_id";

    /**
     * 加密显示字段
     */
    public static final String ENCRY_FIELD_VIEW = "***";

    /**
     * 过滤字段模板: 目前固定库名
     * 库名.表名.字段名
     */
    public static final String DORIS_FIELD_TEM = "%s.`%s`";
    public static final String DORIS_TABLE_FIELD_TEM = "`%s`.`%s`.`%s`";
    public static final String DORIS_TABLE_TEM = "`%s`.`%s`";

    /**
     * 手机解密key
     */
    public static final String MOBILE_DECRYPT_KEY = "ac1pppC3c2JdZD86";
    public static final String USER_MERGE_PREDICT_FIELD_PREFIX = "merge_";
    public static final HashMap<String, String> MERGE_PREDICT_FIELDS_MAP = new HashMap<>() {{
        put("merge_gender", "gender");
        put("merge_age_group", "age");
        put("merge_life_stage", "life_stage");
        put("merge_marriage_status", "");
        put("merge_industry", "industry");
        put("merge_education_level", "education_level");
        put("merge_occupation", "occupation");
        put("merge_consume_level", "consume_level");
        put("merge_consume_intent", "");
        put("merge_geographic_location", "");
        put("merge_interests", "");
    }};

    public static final String DEFAULT_CUSTOMER_GROUP_FOR_ALL = "全员客群";
    public static final String SYSTEM_DEFAULT_USER_ID = "SYSTEM_DEFAULT";

    /**
     * 排序分句
     */
    public static final String ORDER_BY_ID_ASC = "id asc";
    public static final String ORDER_BY_CREATE_TIME_DESC = "create_time desc";
    public static final String ORDER_BY_PRESET_WITH_CREATE_TIME_DESC = "preset desc, create_time desc";
    public static final String ORDER_BY_PRIORITY_ASC = "priority asc";
    public static final String ORDER_BY_EN_FIELD_DESC = "en_field desc";

    /**
     * IDMAPPING固定前缀
     */
    public static final String IDMAPPING_PREFIX = "idmapping_";

    /** id-mapping 非法优先级 **/
    public static final Integer ID_MAPPING_INVALID_PRIORITY = -1;


    /** id-mapping 最大优先级 **/
    public static final int ID_MAPPING_MAX_PRIORITY = 100;

    /** 默认的id-mapping 常量 **/
    public static final List<String> USER_TABLE_ID_MAPPING_DEFAULT_REL = new ArrayList<>() {{
        this.add("mobile");
        this.add("BAIDUID");
        this.add("UNIONID");
        this.add("DEVICEID");
        this.add("IMEI");
        this.add("CUID");
        this.add("MAC");
        this.add("IDFA");
        this.add("OAID");
        this.add("anonymous_id");
    }};
    public static final List<String> KEYUE_RECORD_TABLE_SELF_ID_MAPPING_DEFAULT_REL = new ArrayList<>() {{
        this.add("mobile");
        this.add("BAIDUID");
        this.add("UNIONID");
        this.add("anonymous_id");
    }};
    public static final List<String> KEYUE_RECORD_TABLE_BAIDU_ID_MAPPING_DEFAULT_REL = new ArrayList<>() {{
        this.add("mobile");
        this.add("BAIDUID");
        this.add("UNIONID");
        this.add("IMEI");
        this.add("CUID");
        this.add("MAC");
        this.add("anonymous_id");
    }};
    public static final List<String> AIOB_TABLE_BAIDU_ID_MAPPING_DEFAULT_REL = new ArrayList<>() {{
        this.add("mobile");
        this.add("BAIDUID");
        this.add("IMEI");
        this.add("CUID");
        this.add("MAC");
    }};

    /** Redis id-mapping id_mapping_rule 属性 */
    public static final String REDIS_ID_MAPPING_RULE_PROPERTY = "idMappingRule";

    public static final List<String> MEG_MERGE_SELECT_FIELDS = Lists.newArrayList(
            "mobile", "BAIDUID", "USERID", "CUID", "IMEI", "MAC", "IDFA", "OAID");

    /** 添加字段到 id-mapping 结果表的模板 */
    public static final String ADD_FIELD_TO_ID_MAPPING_TEMPLATE =
            "ALTER TABLE %s ADD COLUMN `%s` array<varchar(255)> NULL default '[]';";

    public static final List<String> MEG_MERGE_MEG_FIELDS = Lists.newArrayList(
            "CUID", "BAIDUID", "USERID", "IMEI", "MAC", "IDFA", "OAID");

    /**
     * 人群扩散配置
     * */
    public static final String SCHEDULER_SYSTEM_USER_ID = "CronScheduler";
    public static final String DORIS_DIFFUSION_PROCESS_TEMPORARY_TABLE_NAME_PREFIX = "process_diffusion_temporary_t_";
    public static final String DORIS_SCORE_FIELD_NAME = "score";
    public static final Integer DIFFUSION_BATCH_SIZE = 100;
    public static final Integer DIFFUSION_CONCURRENCY = 10;
    public static final Integer DIFFUSION_RETRY = 3;
    /** 预测结果临时表字段模板 */
    public static final List<TableFieldMetaInfo> DIFFUSION_PROCESS_TEMPORARY_FIELD_TEMPLATE = new ArrayList<>() {{
        TableFieldMetaInfo oneIdField = new TableFieldMetaInfo();
        oneIdField.setCnField(TABLE_USER_ONE_ID_CNAME);
        oneIdField.setEnField(TABLE_USER_ONE_ID);
        oneIdField.setFieldType(TableFieldTypeEnum.STRING.getValue());
        oneIdField.setDescription(TABLE_USER_ONE_ID_CNAME);
        oneIdField.setIsFilterCriteria(true);
        oneIdField.setIsRequired(false);
        oneIdField.setIsSecrete(false);
        oneIdField.setFromBaidu(false);
        oneIdField.setIsVisable(true);
        oneIdField.setFieldTag(TableFieldTagEnum.PRIMARY.getCode());
        oneIdField.setValueType(TableFieldValueEnum.TEXT.getValueType());
        oneIdField.setDataType("varchar");
        oneIdField.setIsShowValue(true);
        this.add(oneIdField);

        TableFieldMetaInfo scoreField = new TableFieldMetaInfo();
        scoreField.setCnField(DORIS_SCORE_FIELD_NAME);
        scoreField.setEnField(DORIS_SCORE_FIELD_NAME);
        scoreField.setFieldType(TableFieldTypeEnum.NUMBER.getValue());
        scoreField.setDescription(DORIS_SCORE_FIELD_NAME);
        scoreField.setIsFilterCriteria(true);
        scoreField.setIsRequired(false);
        scoreField.setIsSecrete(false);
        scoreField.setFromBaidu(false);
        scoreField.setIsVisable(true);
        scoreField.setFieldTag(TableFieldTagEnum.NULL.getCode());
        scoreField.setValueType(TableFieldValueEnum.NUMBER.getValueType());
        scoreField.setDataType("decimal");
        scoreField.setIsShowValue(true);
        this.add(scoreField);
    }};
    public static final String DIFFUSION_PROCESS_TEMPORARY_FIELD_TEMPLATE_JSON = JsonUtils.toJsonUnchecked(DIFFUSION_PROCESS_TEMPORARY_FIELD_TEMPLATE);
    /** 人群扩展分段统计模板 */
    public static final List<GradingDistributionDTO> DIFFUSION_GRADING_STATISTICS_TEMPLATE = new ArrayList<>() {{
        List<String> grades = List.of("0.9-1.0", "0.8-0.9", "0.7-0.8", "0.6-0.7", "0.5-0.6", "0.4-0.5", "0.3-0.4", "0.2-0.3", "0.1-0.2", "0.0-0.1");
        for (String grade : grades) {
            this.add(new GradingDistributionDTO(grade, 0L, 0.0));
        }
    }};
    public static final String DIFFUSION_GRADING_STATISTICS_TEMPLATE_JSON = JsonUtils.toJsonUnchecked(DIFFUSION_GRADING_STATISTICS_TEMPLATE);
    /** 人群扩展特征默认分级 */
    public static final String DIFFUSION_DEFAULT_GRADE = "0值";
    /** 特征对比个数 */
    public static final Integer CHARACTERISTIC_CONTRACT_COUNT = 6;
    /** 预测特征个数字段名 */
    public static final String PREDICT_CHARACTERISTIC_FIELD_NAME = "predict_count";
    /** 抽样特征个数字段名 */
    public static final String SAMPLE_CHARACTERISTIC_FIELD_NAME = "sample_count";

    public static final HashMap<String, String> DIFFUSION_BASIC_INFO_WITH_SELF_OPERATION = new HashMap<>() {{
        put("age_group", "用户年龄段");
        put("city", "用户城市");
        put("gender", "用户性别");
        put("tags", "用户标签");
        put("area", "商圈");
        put("country", "用户国家");
        put("district", "用户所在区县");
        put("membership_level", "会员等级");
        put("province", "用户省份");
        put("user_type", "用户类型");
        put("income_level", "收入水平");
        put("education_level", "教育水平");
        put("life_stage", "人生阶段");
        put("industry", "所在行业");
        put("occupation", "职业类型");
        put("city_level", "常驻城市线级");
    }};
    public static final HashMap<String, String> DIFFUSION_CHARACTERISTIC_WITH_SELF_OPERATION = new HashMap<>() {{
        put("用户年龄段", "age_group");
        put("用户城市", "city");
        put("用户性别", "gender");
        put("用户标签", "tags");
        put("商圈", "area");
        put("用户国家", "country");
        put("用户所在区县", "district");
        put("会员等级", "membership_level");
        put("用户省份", "province");
        put("用户类型", "user_type");
        put("收入水平", "income_level");
        put("教育水平", "education_level");
        put("人生阶段", "life_stage");
        put("所在行业", "industry");
        put("职业类型", "occupation");
        put("常驻城市线级", "city_level");
    }};
    /** 聚合统计 */
    public static final String AIOB_SESSION_AGG_TABLE_PREFIX = "aiob_conversation_session_agg_";
    public static final String USER_METRIC_AGG_TABLE_PREFIX = "user_metric_";
    public static final String SOP_ANALYSIS_RESULT_TABLE_PREFIX = "aiob_sop_analysis_result_";
    /** 用户统计表字段 */
    public static final String USER_METRIC_AGG_FIELD_LOST_RATE = "weekly_hourly_lost_calls_rate";
    public static final String USER_METRIC_AGG_FIELD_AUTO_ANSWER_CALLS = "weekly_hourly_auto_answer_calls";

    /** 外呼 SOP 统计 */
    public static final String DORIS_AIOB_SOP_NODE_METRIC_TABLE_PREFIX = "aiob_sop_node_metric";
    public static final String DORIS_AIOB_SOP_EDGE_METRIC_TABLE_PREFIX = "aiob_sop_edge_metric";
    public static final String DORIS_AIOB_DEBUG_RECORD_TABLE_PREFIX = "aiob_conversation_record_debug";

    /** 外呼固定参数 */
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String ACCESS_TOKEN_VALUE = "30260b26-ffd2-4bc9-8257-d00f4b71f414";
}
