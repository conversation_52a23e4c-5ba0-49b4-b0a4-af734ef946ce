package com.baidu.keyue.deepsight.config.threadpool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@Slf4j
@EnableAsync
@Configuration
public class AsyncExecutorConfig {

    @Bean(name = "baiduDataPullTaskExecutor")
    public ThreadPoolTaskExecutor baiduDataPullTaskExecutor(
            @Value("${baiduDataPullTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${baiduDataPullTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${baiduDataPullTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("baiduDataPullTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }


    @Bean(name = "customerDiffusionCheckTaskExecutor")
    public ThreadPoolTaskExecutor customerDiffusionCheckTaskExecutor(
            @Value("${customerDiffusionCheckTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${customerDiffusionCheckTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${customerDiffusionCheckTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("customerDiffusionCheckTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Bean(name = "dataPredictionTaskExecutor")
    public ThreadPoolTaskExecutor dataPredictionTaskExecutor(
            @Value("${dataPredictionTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${dataPredictionTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${dataPredictionTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("dataPredictionTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Bean(name = "memoryCalTaskExecutor")
    public ThreadPoolTaskExecutor memoryCalTaskExecutor(
            @Value("${memoryCalTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${memoryCalTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${memoryCalTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("memoryCalTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Bean(name = "metricAggTaskExecutor")
    public ThreadPoolTaskExecutor metricAggTaskExecutor(
            @Value("${metricAggTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${metricAggTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${metricAggTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("metricAggTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Bean(name = "aiobTaskInfoSyncTaskExecutor")
    public ThreadPoolTaskExecutor aiobTaskInfoSyncTaskExecutor(
            @Value("${aiobTaskInfoSyncTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${aiobTaskInfoSyncTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${aiobTaskInfoSyncTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("aiobTaskInfoSyncTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Bean(name = "builtinLabelCalculateTaskExecutor")
    public ThreadPoolTaskExecutor builtinLabelCalculateTaskExecutor(
            @Value("${builtinLabelCalculateTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${builtinLabelCalculateTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${builtinLabelCalculateTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("builtinLabelCalculateTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        return executor;
    }

    /**
     * LLM风险评估专用线程池
     * 用于并发调用LLM风险评估服务，限制并发量<=5
     *
     * @param threadCorePoolSize 核心线程数
     * @param threadMaxPoolSize 最大线程数
     * @param threadWorkQueueSize 队列大小
     * @param keepAliveSeconds 线程保活时间
     * @return ExecutorService
     */
    @Bean(name = "llmRiskAssessmentExecutor")
    public ExecutorService llmRiskAssessmentExecutor(
            @Value("${llmRiskAssessmentTask.coreNumber:3}") int threadCorePoolSize,
            @Value("${llmRiskAssessmentTask.maxNumber:5}") int threadMaxPoolSize,
            @Value("${llmRiskAssessmentTask.queueSize:100}") int threadWorkQueueSize,
            @Value("${llmRiskAssessmentTask.keepAliveSeconds:60}") int keepAliveSeconds) {

        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            threadCorePoolSize,
            threadMaxPoolSize,
            keepAliveSeconds,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(threadWorkQueueSize),
            r -> {
                Thread thread = new Thread(r);
                thread.setName("llm-risk-assessment-" + thread.getId());
                thread.setDaemon(true);
                return thread;
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 队列满时由调用线程执行
        );

        log.info("LLM风险评估线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}, 保活时间: {}秒",
                threadCorePoolSize, threadMaxPoolSize, threadWorkQueueSize, keepAliveSeconds);

        return executor;
    }

}
