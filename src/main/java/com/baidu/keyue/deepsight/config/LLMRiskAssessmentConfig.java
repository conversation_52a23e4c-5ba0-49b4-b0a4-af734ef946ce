package com.baidu.keyue.deepsight.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * LLM风险评估服务配置
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Component
@ConfigurationProperties(prefix = "llm.risk.assessment")
public class LLMRiskAssessmentConfig {

    /**
     * LLM风险评估服务URL
     */
    private String url;

    /**
     * 访问Token
     */
    private String token;

    /**
     * 环境名称
     */
    private String environment = "eval";

    /**
     * 请求超时时间（毫秒）
     */
    private Integer timeout = 10000;
}
