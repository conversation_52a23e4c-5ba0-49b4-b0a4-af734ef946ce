package com.baidu.keyue.deepsight.constants;

/**
 * LLM服务相关常量
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
public final class LLMServiceConstants {

    private LLMServiceConstants() {
        // 工具类，禁止实例化
    }

    /**
     * HTTP请求相关常量
     */
    public static final class HttpConstants {
        /** Content-Type请求头 */
        public static final String CONTENT_TYPE_HEADER = "Content-Type";
        
        /** JSON内容类型 */
        public static final String APPLICATION_JSON = "application/json";
        
        /** Token请求头 */
        public static final String TOKEN_HEADER = "token";
        
        /** 成功状态码 */
        public static final int SUCCESS_CODE = 200;
        
        /** 成功消息 */
        public static final String SUCCESS_MESSAGE = "SUCC";

        private HttpConstants() {
            // 禁止实例化
        }
    }

    /**
     * 风险评估结果映射常量
     */
    public static final class RiskMapping {
        /** LLM返回的低风险标识 */
        public static final String LLM_LOW_RISK = "低风险";
        
        /** LLM返回的中风险标识 */
        public static final String LLM_MEDIUM_RISK = "中风险";
        
        /** LLM返回的高风险标识 */
        public static final String LLM_HIGH_RISK = "高风险";

        private RiskMapping() {
            // 禁止实例化
        }
    }

    /**
     * 错误消息常量
     */
    public static final class ErrorMessages {
        /** LLM服务调用失败 */
        public static final String LLM_SERVICE_CALL_FAILED = "调用LLM风险评估服务失败";
        
        /** LLM服务返回格式错误 */
        public static final String LLM_RESPONSE_FORMAT_ERROR = "LLM服务返回格式错误";
        
        /** LLM服务返回状态码错误 */
        public static final String LLM_RESPONSE_CODE_ERROR = "LLM服务返回状态码错误";
        
        /** 对话内容为空 */
        public static final String CONVERSATION_CONTENT_EMPTY = "对话内容不能为空";

        private ErrorMessages() {
            // 禁止实例化
        }
    }

    /**
     * 默认值常量
     */
    public static final class DefaultValues {
        /** 默认风险等级（当服务调用失败时使用） */
        public static final String DEFAULT_RISK_LEVEL = RiskAssessmentConstants.RiskLevel.LOW_RISK;

        private DefaultValues() {
            // 禁止实例化
        }
    }
}
