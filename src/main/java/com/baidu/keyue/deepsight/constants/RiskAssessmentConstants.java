package com.baidu.keyue.deepsight.constants;

/**
 * 风险评估相关常量
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public final class RiskAssessmentConstants {

    private RiskAssessmentConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 风险等级常量
     */
    public static final class RiskLevel {
        /** 低风险 */
        public static final String LOW_RISK = "投诉风险低";

        /** 中风险 */
        public static final String MEDIUM_RISK = "投诉风险中";

        /** 高风险 */
        public static final String HIGH_RISK = "投诉风险高";
    }

    /**
     * 风险等级枚举，提供类型安全的风险等级比较
     *
     * <AUTHOR>
     */
    public enum RiskLevelEnum {
        /** 低风险 */
        LOW(1, RiskLevel.LOW_RISK),

        /** 中风险 */
        MEDIUM(2, RiskLevel.MEDIUM_RISK),

        /** 高风险 */
        HIGH(3, RiskLevel.HIGH_RISK);

        private final int priority;
        private final String value;

        RiskLevelEnum(int priority, String value) {
            this.priority = priority;
            this.value = value;
        }

        public int getPriority() {
            return priority;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据字符串值获取枚举
         * @param value 风险等级字符串
         * @return 对应的枚举，如果不匹配则返回null
         */
        public static RiskLevelEnum fromValue(String value) {
            if (value == null) {
                return null;
            }
            for (RiskLevelEnum level : values()) {
                if (level.value.equals(value)) {
                    return level;
                }
            }
            return null;
        }

        /**
         * 获取两个风险等级中较高的一个
         * @param level1 风险等级1
         * @param level2 风险等级2
         * @return 较高的风险等级，如果都为null则返回null
         */
        public static RiskLevelEnum getHigher(RiskLevelEnum level1, RiskLevelEnum level2) {
            if (level1 == null && level2 == null) {
                return null;
            }
            if (level1 == null) {
                return level2;
            }
            if (level2 == null) {
                return level1;
            }
            return level1.priority >= level2.priority ? level1 : level2;
        }

        /**
         * 获取两个风险等级字符串中较高的一个
         * @param risk1 风险等级字符串1
         * @param risk2 风险等级字符串2
         * @return 较高的风险等级字符串，如果都为空则返回null
         */
        public static String getHigherRisk(String risk1, String risk2) {
            RiskLevelEnum level1 = fromValue(risk1);
            RiskLevelEnum level2 = fromValue(risk2);
            RiskLevelEnum higher = getHigher(level1, level2);
            return higher != null ? higher.getValue() : null;
        }
    }

    /**
     * 时间风险阈值常量（天数）
     */
    public static final class TimeRiskThreshold {
        /** 低风险阈值：7天内 */
        public static final int LOW_RISK_DAYS = 7;
        
        /** 中风险阈值：30天内 */
        public static final int MEDIUM_RISK_DAYS = 30;
    }

}
