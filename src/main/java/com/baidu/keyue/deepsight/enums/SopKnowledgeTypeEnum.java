package com.baidu.keyue.deepsight.enums;

import lombok.Getter;

/**
 * SOP知识类型枚举
 */
@Getter
public enum SopKnowledgeTypeEnum {

    INTENT(1, "意图"),
    FAQ(2, "FAQ"),
    INTENTION_TAG(3, "意向标签"),
    DOCUMENT(4, "文档"),
    OTHER(5, "其他");

    private final Integer code;
    private final String desc;

    SopKnowledgeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code查找枚举
     */
    public static SopKnowledgeTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SopKnowledgeTypeEnum knowledgeType : values()) {
            if (knowledgeType.getCode().equals(code)) {
                return knowledgeType;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     */
    public static String getDescByCode(Integer code) {
        SopKnowledgeTypeEnum knowledgeType = fromCode(code);
        return knowledgeType != null ? knowledgeType.getDesc() : "未知";
    }
}