package com.baidu.keyue.deepsight.dorisdb.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * SOP知识引用分析结果实体
 */
@Data
public class SopAnalysisResult {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 机器人ID
     */
    private String robotId;

    /**
     * 机器人版本
     */
    private String robotVer;

    /**
     * 话题ID
     */
    private String topicId;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 知识类型：1意图,2faq,3意向标签,4文档,5其他
     */
    private Integer knowledgeType;

    /**
     * 知识内容
     */
    private String knowledgeText;

    /**
     * 查询ID
     */
    private String queryId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 知识目录ID
     */
    private String knowledgeDirId;

    /**
     * 知识标签
     */
    private String knowledgeTag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 内容
     */
    private String content;
}