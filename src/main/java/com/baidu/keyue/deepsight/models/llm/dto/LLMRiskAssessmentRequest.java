package com.baidu.keyue.deepsight.models.llm.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LLM风险评估请求DTO
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LLMRiskAssessmentRequest {

    /**
     * 环境名称
     */
    private String environmentName;

    /**
     * 请求参数
     */
    private Parameters parameters;

    /**
     * 请求参数内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Parameters {
        /**
         * 对话内容
         */
        private String dialog;
    }

    /**
     * 构建LLM风险评估请求
     * 
     * @param environmentName 环境名称
     * @param conversationContent 对话内容
     * @return LLM风险评估请求对象
     */
    public static LLMRiskAssessmentRequest build(String environmentName, String conversationContent) {
        Parameters parameters = new Parameters(conversationContent);
        return new LLMRiskAssessmentRequest(environmentName, parameters);
    }
}
