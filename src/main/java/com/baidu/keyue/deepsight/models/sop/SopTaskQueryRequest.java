package com.baidu.keyue.deepsight.models.sop;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

/**
 * SOP离线分析任务查询请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SopTaskQueryRequest extends BasePageRequest {

    /**
     * 任务状态：1-执行中，2-计算失败，3-成功
     */
    @Range(min = 1, max = 3, message = "任务状态参数不正确")
    private Byte status;

    private String robotId;

    /**
     * 名称搜索
     */
    private String keyword;
}
