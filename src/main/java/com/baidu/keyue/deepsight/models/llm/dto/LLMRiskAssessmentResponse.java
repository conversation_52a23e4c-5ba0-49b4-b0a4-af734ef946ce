package com.baidu.keyue.deepsight.models.llm.dto;

import com.baidu.keyue.deepsight.constants.LLMServiceConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LLM风险评估响应DTO
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LLMRiskAssessmentResponse {

    /**
     * 响应数据
     */
    private ResponseData data;

    /**
     * 响应状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应描述
     */
    private String desc;

    /**
     * 响应数据内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResponseData {
        /**
         * 输出结果（JSON格式字符串）
         */
        private String output;

        /**
         * 推理内容
         */
        private String reasoningContent;

        /**
         * 开始时间
         */
        private String startTime;

        /**
         * 结束时间
         */
        private String endTime;

        /**
         * 提示词Token数
         */
        private Integer promptTokens;

        /**
         * 完成Token数
         */
        private Integer completionTokens;

        /**
         * 总Token数
         */
        private Integer totalTokens;

        /**
         * 记录ID
         */
        private Long recordID;

        /**
         * 会话ID
         */
        private String sessionID;

        /**
         * 搜索结果
         */
        private Object searchResults;
    }

    /**
     * 检查响应是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 200 && LLMServiceConstants.HttpConstants.SUCCESS_MESSAGE.equals(msg);
    }
}
