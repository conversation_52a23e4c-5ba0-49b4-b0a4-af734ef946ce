package com.baidu.keyue.deepsight.models.sop;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * SOP意图和标签聚合查询请求
 */
@Data
public class SopAnalysisAggregateRequest {

    /**
     * 机器人ID - 精确匹配
     */
    @NotBlank(message = "机器人ID不能为空")
    private String robotId;

    /**
     * 任务ID - 精确匹配
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 机器人版本 - 精确匹配
     */
    @NotBlank(message = "机器人版本不能为空")
    private String robotVer;

    /**
     * 意向特殊标识值（用于区分有意向和无意向的标签）
     */
    private String intentionFlag = "意向";
}