package com.baidu.keyue.deepsight.models.sop;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

/**
 * SOP知识引用分析FAQ查询请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SopAnalysisResultQueryRequest extends BasePageRequest {

    /**
     * 机器人ID - 精确匹配
     */
    @NotBlank(message = "机器人ID不能为空")
    private String robotId;

    /**
     * 任务ID - 精确匹配
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 机器人版本 - 精确匹配
     */
    @NotBlank(message = "机器人版本不能为空")
    private String robotVer;

    /**
     * 知识类型：2-FAQ查询
     */
    @Range(min = 1, max = 3, message = "知识类型不正确")
    private Integer knowledgeType;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 知识标签
     */
    private String knowledgeTag;
}
