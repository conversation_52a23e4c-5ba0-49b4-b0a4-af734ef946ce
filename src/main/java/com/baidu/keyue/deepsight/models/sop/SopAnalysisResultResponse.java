package com.baidu.keyue.deepsight.models.sop;

import com.baidu.keyue.deepsight.dorisdb.entity.SopAnalysisResult;
import com.baidu.keyue.deepsight.enums.SopKnowledgeTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * SOP知识引用分析响应
 */
@Data
public class SopAnalysisResultResponse {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 机器人ID
     */
    private String robotId;

    /**
     * 机器人版本
     */
    private String robotVer;

    /**
     * 话题ID
     */
    private String topicId;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 知识类型：1意图,2faq,3意向标签,4文档,5其他
     */
    private Integer knowledgeType;

    /**
     * 知识类型描述
     */
    private String knowledgeTypeDesc;

    /**
     * 知识内容
     */
    private String knowledgeText;

    /**
     * 查询ID
     */
    private String queryId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 知识目录ID
     */
    private String knowledgeDirId;

    /**
     * 知识标签
     */
    private String knowledgetTag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 内容
     */
    private String content;

    /**
     * 统计计数 - 相同组合的出现次数
     * 基于：taskId + robotId + robotVer + knowledgeText + knowledgeType + nodeId + topicId
     */
    private Long count;

    /**
     * 出现概率（百分比）
     */
    private BigDecimal probability;

    /**
     * 从实体转换为响应对象
     */
    public static SopAnalysisResultResponse fromEntity(SopAnalysisResult entity) {
        SopAnalysisResultResponse response = new SopAnalysisResultResponse();
        response.setTaskId(entity.getTaskId());
        response.setRobotId(entity.getRobotId());
        response.setRobotVer(entity.getRobotVer());
        response.setTopicId(entity.getTopicId());
        response.setNodeId(entity.getNodeId());
        response.setKnowledgeType(entity.getKnowledgeType());
        response.setKnowledgeTypeDesc(SopKnowledgeTypeEnum.getDescByCode(entity.getKnowledgeType()));
        response.setKnowledgeText(entity.getKnowledgeText());
        response.setQueryId(entity.getQueryId());
        response.setSessionId(entity.getSessionId());
        response.setKnowledgeDirId(entity.getKnowledgeDirId());
        response.setKnowledgetTag(entity.getKnowledgeTag());
        response.setCreateTime(entity.getCreateTime());
        response.setUpdateTime(entity.getUpdateTime());
        response.setContent(entity.getContent());
        return response;
    }

}