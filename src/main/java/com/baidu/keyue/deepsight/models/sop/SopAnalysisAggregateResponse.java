package com.baidu.keyue.deepsight.models.sop;

import lombok.Data;

import java.util.List;

/**
 * SOP意图和标签聚合查询响应
 */
@Data
public class SopAnalysisAggregateResponse {

    /**
     * 所有意图列表（knowledgeType=1）
     */
    private List<IntentInfo> intents;

    /**
     * 有意向的标签列表（knowledgeType=3且包含意向标识）
     */
    private List<TagInfo> intentionTags;

    /**
     * 无意向的标签列表（knowledgeType=3且不包含意向标识）
     */
    private List<TagInfo> nonIntentionTags;

    /**
     * 意图信息
     */
    @Data
    public static class IntentInfo {
        /**
         * 意图内容
         */
        private String intentText;
        
        /**
         * 出现次数
         */
        private Long count;
        
        /**
         * 出现概率（百分比）
         */
        private Double probability;
    }

    /**
     * 标签信息
     */
    @Data
    public static class TagInfo {
        /**
         * 标签内容
         */
        private String tagText;
        
        /**
         * 出现次数
         */
        private Long count;
        
        /**
         * 出现概率（百分比）
         */
        private Double probability;
        
        /**
         * 是否包含意向标识
         */
        private Boolean hasIntention;
    }
}