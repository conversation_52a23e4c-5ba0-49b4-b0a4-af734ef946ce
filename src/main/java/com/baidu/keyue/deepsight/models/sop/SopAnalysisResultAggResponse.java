package com.baidu.keyue.deepsight.models.sop;

import com.baidu.keyue.deepsight.dorisdb.entity.SopAnalysisResult;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * SOP知识引用分析响应
 */
@Data
public class SopAnalysisResultAggResponse {

    /**
     * faq topN 的分析结果
     */
    private List<SopAnalysisResultResponse> faqSopTopAnalysis;

    /**
     * 有意向 topN 的分析结果
     */
    private Map<String, Integer> intention;

    /**
     * 无意向 topN 的分析结果
     */
    private Map<String, Integer> noIntention;




}