package com.baidu.keyue.deepsight.models.agg;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AiobAggMetricCal {
    private String oneId;

    /**
     * 接通率
     */
    private Float connectRate;
    /**
     * 搜轮挂断率
     */
    private Float firstRoundHangupRate;
    /**
     * 平均对话轮次
     */
    private Float avgRounds;
    /**
     * 平均通话时长
     */
    private Float avgDuration;
    /**
     * 各时间段接通情况
     */
    private Map<String, AiobAggMetricTimeBucket> timeBucketStatistics;

    /**
     * 最后一次呼叫日期
     */
    private LocalDate lastCallDate;

    /**
     * 拨打总次数
     */
    private Long totalCalls = 0L;
    /**
     * 接通次数
     */
    private Long totalConnectedCalls = 0L;
    /**
     * 搜轮挂断次数(对话轮次是1 ，且挂断的)
     */
    private Long totalFirstRoundHangup = 0L;

    /**
     * 接通-对话轮次
     */
    private Long totalRounds = 0L;
    /**
     * 接通-通话总时长(秒)
     */
    private Long totalDurationTime = 0L;

    /**
     * 用户每周每小时的小秘书接通数统计 (7×24矩阵)
     * 外层List表示星期（0=周日，6=周六）
     * 内层List表示小时（0-23）
     */
    private List<List<Long>> weeklyHourlyAutoAnswerCalls;

    /**
     * 用户每周每小时的未接通率 (7×24矩阵)
     * 外层List表示星期（0=周日，6=周六）
     * 内层List表示小时（0-23）
     */
    private List<List<Float>> weeklyHourlyLostCallsRate;

    /**
     * 每周每小时的呼叫数据收集器 (7×24矩阵)
     * [周几][小时] -> [总呼叫数, 总接通数, 小秘书接通数]
     */
    @Builder.Default
    private Long[][][] weeklyHourlyCallData = initializeCallDataArray(); // [总呼叫数, 总接通数, 小秘书接通数]

    /**
     * 接通-通话时段
     */
    private Map<String, Long> timeBucketMap = new HashMap<String, Long>() {{
        this.put("0-1", 0L);
        this.put("1-2", 0L);
        this.put("2-3", 0L);
        this.put("3-4", 0L);
        this.put("4-5", 0L);
        this.put("5-6", 0L);
        this.put("6-7", 0L);
        this.put("7-8", 0L);
        this.put("8-9", 0L);
        this.put("9-10", 0L);
        this.put("10-11", 0L);
        this.put("11-12", 0L);
        this.put("12-13", 0L);
        this.put("13-14", 0L);
        this.put("14-15", 0L);
        this.put("15-16", 0L);
        this.put("16-17", 0L);
        this.put("17-18", 0L);
        this.put("18-19", 0L);
        this.put("19-20", 0L);
        this.put("20-21", 0L);
        this.put("21-22", 0L);
        this.put("22-23", 0L);
        this.put("23-24", 0L);
    }};

    /**
     * 静态方法：初始化呼叫数据数组
     */
    private static Long[][][] initializeCallDataArray() {
        Long[][][] data = new Long[7][24][3];
        for (int day = 0; day < 7; day++) {
            for (int hour = 0; hour < 24; hour++) {
                data[day][hour][0] = 0L; // 总呼叫数
                data[day][hour][1] = 0L; // 总接通数
                data[day][hour][2] = 0L; // 小秘书接通数
            }
        }
        return data;
    }

    /**
     * 确保每周每小时呼叫数据收集器已初始化
     */
    private void ensureWeeklyHourlyCallDataInitialized() {
        if (weeklyHourlyCallData == null) {
            weeklyHourlyCallData = initializeCallDataArray();
        }
    }

    /**
     * 添加每周每小时的呼叫数据
     * @param callDate 呼叫日期 (yyyy-MM-dd格式)
     * @param timeBucket 时间桶 (如"9-10")
     * @param totalCalls 总呼叫数
     * @param totalConnectedCalls 总接通数
     * @param autoAnswerCalls 小秘书接通数
     */
    public void appendWeeklyHourlyData(String callDate, String timeBucket, Long totalCalls, Long totalConnectedCalls, Long autoAnswerCalls) {
        if (callDate == null || timeBucket == null) {
            return;
        }

        // 确保数据收集器已初始化
        ensureWeeklyHourlyCallDataInitialized();

        try {
            // 解析日期获取星期几 (0=周日, 1=周一, ..., 6=周六)
            String[] split = callDate.split("T");
            LocalDate date = LocalDate.parse(split[0]);
            int dayOfWeek = date.getDayOfWeek().getValue() % 7; // Java中周一=1，转换为周日=0

            // 解析时间桶获取小时 (如"9-10" -> 9)
            String[] timeParts = timeBucket.split("-");
            if (timeParts.length != 2) {
                return;
            }
            int hour = Integer.parseInt(timeParts[0]);
            if (hour < 0 || hour >= 24) {
                return;
            }

            // 累加数据
            if (totalCalls != null) {
                weeklyHourlyCallData[dayOfWeek][hour][0] += totalCalls;
            }
            if (totalConnectedCalls != null) {
                weeklyHourlyCallData[dayOfWeek][hour][1] += totalConnectedCalls;
            }
            if (autoAnswerCalls != null) {
                weeklyHourlyCallData[dayOfWeek][hour][2] += autoAnswerCalls;
            }
        } catch (Exception e) {
            // 忽略解析错误，继续处理其他数据
        }
    }

    public void appendTimeBucket(String timeBucket, Object calls) {
        Long num = 0L;
        if (Objects.nonNull(calls)) {
            num = (Long) calls;
        }
        if (timeBucketMap.containsKey(timeBucket)) {
            timeBucketMap.put(timeBucket, num + timeBucketMap.get(timeBucket));
        } else {
            timeBucketMap.put(timeBucket, num);
        }
    }

    public void appendTotalCalls(Object totalCalls) {
        if (Objects.nonNull(totalCalls)) {
            this.totalCalls += (Long) totalCalls;
        }
    }

    public void appendTotalConnectedCalls(Object totalConnectedCalls) {
        if (Objects.nonNull(totalConnectedCalls)) {
            this.totalConnectedCalls += (Long) totalConnectedCalls;
        }
    }

    public void appendTotalFirstRoundHangup(Object totalFirstRoundHangup) {
        if (Objects.nonNull(totalFirstRoundHangup)) {
            this.totalFirstRoundHangup += (Long) totalFirstRoundHangup;
        }
    }

    public void appendTotalRounds(Object totalRounds) {
        if (Objects.nonNull(totalRounds)) {
            this.totalRounds += (Long) totalRounds;
        }
    }

    public void appendTotalDurationTime(Object totalDurationTime) {
        if (Objects.nonNull(totalDurationTime)) {
            this.totalDurationTime += (Long) totalDurationTime;
        }
    }

    /**
     * 统计通话相关指标。
     *
     * <p>该方法计算以下指标：
     * <ul>
     * <li>接通率（connectRate）：成功接通的通话数占总通话数的比例。</li>
     * <li>一轮挂断率（firstRoundHangupRate）：一轮通话即挂断的次数占总通话数的比例。</li>
     * <li>平均轮数（avgRounds）：总轮数除以成功接通的通话数。</li>
     * <li>平均通话时长（avgDuration）：总通话时长除以成功接通的通话数。</li>
     * </ul>
     *
     * <p>此外，该方法还会根据时间桶（timeBucketMap）统计每个时间桶的通话次数和比例，并存储到timeBucketStatistics中。
     * 最后，调用calculateWeeklyHourlyMatrices方法计算7×24矩阵数据。
     */
    public void statistics() {
        if (totalCalls == 0) {
            this.connectRate = 0F;
            this.firstRoundHangupRate = 0F;
        } else {
            this.connectRate = totalConnectedCalls * 1.0f / totalCalls;
            this.firstRoundHangupRate = totalFirstRoundHangup * 1.0f / totalCalls;
        }

        if (totalConnectedCalls == 0) {
            this.avgRounds = 0F;
            this.avgDuration = 0F;
        } else {
            this.avgRounds = totalRounds * 1.0f / totalConnectedCalls;
            this.avgDuration = totalDurationTime * 1.0f / totalConnectedCalls;
        }

        timeBucketStatistics = new HashMap<>();
        if (totalConnectedCalls == 0) {
            for (Map.Entry<String, Long> entry : timeBucketMap.entrySet()) {
                timeBucketStatistics.put(entry.getKey(), new AiobAggMetricTimeBucket(0L, 0F));
            }
        } else {
            for (Map.Entry<String, Long> entry : timeBucketMap.entrySet()) {
                float percent = entry.getValue() * 1.0f / totalConnectedCalls;
                timeBucketStatistics.put(entry.getKey(), new AiobAggMetricTimeBucket(entry.getValue(), percent));
            }
        }

        // 计算7×24矩阵数据
        calculateWeeklyHourlyMatrices();
    }

    /**
     * 计算每周每小时的接通数和未接通率矩阵
     */
    private void calculateWeeklyHourlyMatrices() {
        // 确保数据收集器已初始化
//        ensureWeeklyHourlyCallDataInitialized();

        this.weeklyHourlyAutoAnswerCalls = new ArrayList<>();
        this.weeklyHourlyLostCallsRate = new ArrayList<>();

        for (int day = 0; day < 7; day++) {
            List<Long> dayAutoAnswerCalls = new ArrayList<>();
            List<Float> dayLostCallsRate = new ArrayList<>();

            for (int hour = 0; hour < 24; hour++) {
                Long totalCallsInHour = weeklyHourlyCallData[day][hour][0];
                Long totalConnectedCallsInHour = weeklyHourlyCallData[day][hour][1];
                Long autoAnswerCallsInHour = weeklyHourlyCallData[day][hour][2];

                // 小秘书接通数直接使用
                dayAutoAnswerCalls.add(autoAnswerCallsInHour);

                // 计算未接通率，精确到小数点后两位
                Float lostRate = 0.0f;
                if (totalCallsInHour > 0) {
                    Long lostCalls = totalCallsInHour - totalConnectedCallsInHour;
                    float rawRate = lostCalls * 1.0f / totalCallsInHour;
                    // 四舍五入到小数点后两位
                    lostRate = Math.round(rawRate * 100.0f) / 100.0f;
                }
                dayLostCallsRate.add(lostRate);
            }

            this.weeklyHourlyAutoAnswerCalls.add(dayAutoAnswerCalls);
            this.weeklyHourlyLostCallsRate.add(dayLostCallsRate);
        }
    }
}
