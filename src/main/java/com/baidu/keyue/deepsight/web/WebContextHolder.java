package com.baidu.keyue.deepsight.web;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.constants.BuiltinLabelConstants;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;

/**
 * <AUTHOR>
 * @className WebContextHolder
 * @description 用户/租户上下文
 * @date 2025/1/12 13:54
 */
public class WebContextHolder {

    private static final ThreadLocal<DeepSightWebContext> CONTEXT_HOLDER = new InheritableThreadLocal<>();

    public static UserAuthInfo getUserAuthInfo() {
        return CONTEXT_HOLDER.get();
    }
    public static DeepSightWebContext getDeepSightWebContext() {
        return CONTEXT_HOLDER.get();
    }

    public static void setDeepSightWebContext(DeepSightWebContext deepSightWebContext) {
        CONTEXT_HOLDER.set(deepSightWebContext);
    }

    public static void clean() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 获取租户ID
     * @return
     */

    public static String getTenantId(){
        return String.valueOf(CONTEXT_HOLDER.get().getTenantId());
    }

    /**
     * 获取用户ID
     * @return
     */
    public static String getUserId(){
        Long userId = CONTEXT_HOLDER.get().getUserId();
        // 如果是系统用户（-1L），返回SYSTEM_DEFAULT
        if (userId != null && userId.equals(BuiltinLabelConstants.SystemUser.SYSTEM_USER_ID)) {
            return Constants.SYSTEM_DEFAULT_USER_ID;
        }
        return String.valueOf(userId);
    }

    /**
     * 获取请求ID
     * @return
     */
    public static String getRequestId(){
        return CONTEXT_HOLDER.get() == null ? null : CONTEXT_HOLDER.get().getRequestId();
    }

    /**
     * 获取用户姓名
     * @return
     */
    public static String getUserName(){
        return CONTEXT_HOLDER.get() == null ? null : CONTEXT_HOLDER.get().getUserName();
    }
}
