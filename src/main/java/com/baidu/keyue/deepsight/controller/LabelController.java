package com.baidu.keyue.deepsight.controller;

import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseRecordResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogResponse;
import com.baidu.keyue.deepsight.models.label.DeleteLabelRequest;
import com.baidu.keyue.deepsight.models.label.GetLabelDetailRequest;
import com.baidu.keyue.deepsight.models.label.GetLabelDistributeRequest;
import com.baidu.keyue.deepsight.models.label.LabelDetail;
import com.baidu.keyue.deepsight.models.label.LabelDistribute;
import com.baidu.keyue.deepsight.models.label.LabelOriginalDetail;
import com.baidu.keyue.deepsight.models.label.LabelUserListRequest;
import com.baidu.keyue.deepsight.models.label.ListLabelBriefRequest;
import com.baidu.keyue.deepsight.models.label.NewLabelRequest;
import com.baidu.keyue.deepsight.models.label.UpdateLabelRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelService;
import com.baidu.keyue.deepsight.service.label.LabelCalculateService;
import com.baidu.keyue.deepsight.service.label.LabelService;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 标签管理API
 * @className: LabelController
 * @description: 标签管理API
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/label")
public class LabelController {

    private final LabelService labelService;
    private final LabelCalculateService labelCalculateService;
    private final BuiltinLabelService builtinLabelService;
    private final TenantInfoService tenantInfoService;


    /**
     * 新建标签
     *
     * @param request 新建标签请求体
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public BaseResponse<Void> createLabel(@RequestBody @Valid NewLabelRequest request) {
        labelService.createLabel(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 标签列表查询
     *
     * @param request 列表查询条件
     * @return BasePageResponse<LabelBrief> 返回标签分页列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public BasePageResponse<LabelDetail> listLabel(@RequestBody ListLabelBriefRequest request) {
        return BasePageResponse.of(labelService.listLabel(request));
    }

    /**
     * 标签树形结构查询，保留标签目录和标签
     * @param request 列表查询条件
     * @return BaseResponse<ListCatalogResponse> 返回标签目录列表
     * */
    @RequestMapping(value = "/tree", method = RequestMethod.POST)
    public BaseResponse<ListCatalogResponse> listLabelTree(@RequestBody ListCatalogRequest request) {
        return BaseResponse.of(labelService.listLabelTree(request, false));
    }

    /**
     * 删除标签
     *
     * @param request 删除标签的 ID
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public BaseResponse<Void> deleteLabel(@RequestBody @Valid DeleteLabelRequest request) {
        Label label = labelService.getLabelByTenantAndLabelId(request.getLabelId());
        labelService.deleteLabel(label);
        labelCalculateService.cancelExecByManual(label);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 查询标签详情
     *
     * @param request 标签 ID
     * @return BaseResponse<LabelDetail> 标签详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public BaseResponse<LabelOriginalDetail> labelDetail(@RequestBody @Valid GetLabelDetailRequest request) {
        return BaseResponse.of(labelService.getLabelDetail(request));
    }

    /**
     * 查询标签分布详情
     *
     * @param request 标签 ID
     * @return BaseResponse<LabelDistribute> 标签分布详情
     */
    @RequestMapping(value = "/distribution", method = RequestMethod.POST)
    public BaseResponse<LabelDistribute> labelDistribution(@RequestBody @Valid GetLabelDistributeRequest request) {
        return BaseResponse.of(labelService.labelDistribution(request));
    }

    /**
     * 标签更新
     *
     * @param request 更新内容
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public BaseResponse<Void> updateLabel(@RequestBody @Valid UpdateLabelRequest request) {
        labelService.updateLabel(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 手动执行标签计算
     *
     * @param request 标签 ID
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/manual", method = RequestMethod.POST)
    public BaseResponse<Void> manual(@RequestBody @Valid GetLabelDetailRequest request) {
        labelCalculateService.execByManual(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 手动取消执行标签计算
     *
     * @param request 标签 ID
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/manualCancel", method = RequestMethod.POST)
    public BaseResponse<Void> manualCancel(@RequestBody @Valid GetLabelDetailRequest request) {
        Label label = labelService.getLabelByTenantAndLabelId(request.getLabelId());
        labelCalculateService.cancelExecByManual(label);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 标签用户列表查询
     *
     * @param request 用户列表查询条件
     * @return BasePageResponse<Map<String, String>> 返回标签用户分页列表
     */
    @PostMapping(value = "/r/userList")
    public BaseResponse<BaseRecordResponse> labelUserList(@RequestBody @Valid LabelUserListRequest request) {
        log.info("labelUserList request:{}", JSONUtil.toJsonStr(request));
        return BaseResponse.of(labelService.labelUserList(request, WebContextHolder.getTenantId()));
    }

    /**
     * 测试预置标签更新
     */
    @GetMapping(value = "/testMultiThreadProcessing")
    public void testMultiThreadProcessing(@RequestParam(value = "tenantIds", required = false) List<String> tenantIds) {
        log.info("testMultiThreadProcessing start");
        List<TenantInfo> tenantInfos = new ArrayList<>();
        if (CollectionUtils.isEmpty(tenantIds)) {
            tenantInfos = tenantInfoService.getAllTenantInfo();
        }
        for (String tenantId : tenantIds) {
            TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(tenantId);
            tenantInfos.add(tenantInfo);
        }
        builtinLabelService.triggerBuiltinLabelCalculationAsync(tenantInfos);
        log.info("testMultiThreadProcessing end");
    }
}
