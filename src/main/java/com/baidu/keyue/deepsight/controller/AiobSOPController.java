package com.baidu.keyue.deepsight.controller;

import java.util.HashMap;
import java.util.List;

import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressRequest;
import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressResponse;
import com.baidu.keyue.deepsight.models.sop.SOPNodeConfirmRequest;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictRequest;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictResponse;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisCompletionTimeResponse;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisResultAggResponse;
import com.baidu.keyue.deepsight.models.sop.SopBaseRequest;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeResponse;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailResponse;
import com.baidu.keyue.deepsight.models.sop.SopSankeyMetaResponse;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskCfgStatusEnum;
import com.baidu.keyue.deepsight.models.sop.SopUserConfigUpdateRequest;
import com.baidu.keyue.deepsight.models.sop.SopUserDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskCreateRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskQueryRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskUpdateRequest;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisResultQueryRequest;
import com.baidu.keyue.deepsight.models.sop.SopAnalysisResultResponse;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.service.sop.impl.AiobSopStatisticService;
import com.baidu.keyue.deepsight.service.sop.SopTaskCfgService;
import com.baidu.keyue.deepsight.service.sop.SopAnalysisResultService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 外呼SOP相关接口
 * @Description 外呼 SOP 统计
 * <AUTHOR>
 * @Date 2025/5/08 16:51
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/sop")
public class AiobSOPController {

    private final AiobSopStatisticService aiobSopStatisticService;
    private final AiobSOPService aiobSOPService;
    private final SopTaskCfgService sopTaskCfgService;
    private final SopAnalysisResultService sopAnalysisResultService;

    /**
     * 【灵活画布】整体统计数据
     * @param request SopFlexibleWholeRequest
     * @return BaseResponse<SopFlexibleWholeResponse>
     */
    @PostMapping("/r/flexible/whole")
    public BaseResponse<SopFlexibleWholeResponse> flexibleWholeData(@RequestBody @Valid SopFlexibleWholeRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.flexibleWholeData(tenantId, request));
    }

    /**
     * 【快捷场景】桑基图整体统计数据
     * @param request SopSankeyWholeRequest
     * @return BaseResponse<SopSankeyWholeResponse>
     */
    @PostMapping("/r/sankey/whole")
    public BaseResponse<SopSankeyWholeResponse> sankeyWholeData(@RequestBody @Valid SopSankeyWholeRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.sankeyWholeData(tenantId, request));
    }

    /**
     * 【快捷场景】桑基图步骤和节点信息
     * @param request SopSankeyWholeRequest
     * @return BaseResponse<SopSankeyMetaResponse>
     */
    @PostMapping("/r/sankey/sopMeta")
    public BaseResponse<SopSankeyMetaResponse> sankeySopMeta(@RequestBody @Valid SopSankeyWholeRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSOPService.listSOPMeta(tenantId, request.getTaskId(), request.getRobotVer()));
    }

    /**
     * 【快捷场景、灵活画布】节点详情统计 (包含上下游节点流量分布、每日流量分布)
     * @param request SopNodeDetailRequest
     * @return BaseResponse<SopNodeDetailResponse>
     */
    @PostMapping("/r/nodeDetail")
    public BaseResponse<SopNodeDetailResponse> nodeDetailStatistics(@RequestBody @Valid SopNodeDetailRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.nodeDetailStatistics(tenantId, request));
    }

    /**
     * 【快捷场景、灵活画布】分析进度
     * @param request SopNodeDetailRequest
     * @return BaseResponse<SopNodeDetailResponse>
     */
    @PostMapping("/r/analyseProgress")
    public BaseResponse<SOPAnalyseProgressResponse> analyseProgress(@RequestBody @Valid SOPAnalyseProgressRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.analyseProgress(tenantId, request));
    }

    /**
     * 获取机器人版本列表
     */
    @PostMapping("/r/robotVersionList")
    public BaseResponse<List<SopWholeRobotVersionResponse>> wholeRobotVersion(@RequestBody @Valid SopWholeRobotVersionRequest request) {
        return BaseResponse.of(aiobSOPService.listRobotVersion(request));
    }

    /**
     * 节点预测
     */
    @PostMapping("/r/nodePredict")
    public BaseResponse<SOPNodePredictResponse> predictNode(@RequestBody @Valid SOPNodePredictRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSOPService.predictNode(tenantId, request));
    }

    /**
     * 确认节点
     */
    @PostMapping("/r/nodeConfirm")
    public BaseResponse<Void> editNode(@RequestBody @Valid SOPNodeConfirmRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        aiobSOPService.confirmNode(tenantId, request.getTaskId(), request.getRule(), request.getMarkdown(), request.getRobotVer());
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 【快捷场景、灵活画布】更新分析设置
     * @param request SopUserConfigUpdateRequest
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/userConfig/update")
    public BaseResponse<Void> updateUserConfig(@RequestBody @Valid SopUserConfigUpdateRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        aiobSOPService.updateUserConfig(tenantId, request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 【快捷场景、灵活画布】获取分析设置
     * @param request SopBaseRequest
     * @return BaseResponse<SopUserConfigUpdateRequest>
     */
    @PostMapping("/r/userConfig/get")
    public BaseResponse<SopUserConfigUpdateRequest> getUserConfig(@RequestBody @Valid SopBaseRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSOPService.getUserConfig(tenantId, request));
    }

    /**
     * 用户明细
     * @param request SopNodeDetailRequest
     * @return BasePageResponse<Map < String, String>>
     */
    @PostMapping("/r/userDetail")
    public BasePageResponse<HashMap<String, Object>> nodeUserDetail(@RequestBody @Valid SopUserDetailRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        if (request.getIntent()) {
            request.setCurrNodeId(aiobSopStatisticService.intentTagDecrypt(request.getCurrNodeId()));
        }
        List<String> idTransferRes = aiobSopStatisticService.taskIdTransfer(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer());
        request.setRobotId(idTransferRes.get(0));
        request.setRobotVer(idTransferRes.get(1));
        return BasePageResponse.of(aiobSOPService.getUserDetailV2(tenantId, request));
    }

    // ================= 离线分析任务相关接口 =================

    /**
     * 创建离线分析任务
     * @param request SopTaskCreateRequest
     * @return BaseResponse<Long>
     */
    @PostMapping("/w/offlineTask/create")
    public BaseResponse<Long> createOfflineTask(@RequestBody @Valid SopTaskCreateRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        String creator = WebContextHolder.getUserAuthInfo().getUserName();
        Long taskId = sopTaskCfgService.createOfflineTask(tenantId, request, creator);
        return BaseResponse.of(taskId);
    }

    /**
     * 分页查询离线分析任务
     * @param request SopTaskQueryRequest
     * @return BasePageResponse<SopTaskResponse>
     */
    @PostMapping("/r/offlineTask/query")
    public BasePageResponse.Page<SopTaskResponse> queryOfflineTasks(@RequestBody @Valid SopTaskQueryRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return sopTaskCfgService.queryOfflineTasks(tenantId, request);

    }

    /**
     * 根据ID查询离线分析任务详情
     * @param id 分析任务ID
     * @return BaseResponse<SopTaskResponse>
     */
    @PostMapping("/r/offlineTask/detail")
    public BaseResponse<SopTaskResponse> getOfflineTaskDetail(@RequestBody Long id) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        SopTaskResponse response = sopTaskCfgService.getOfflineTaskById(tenantId, id);
        return BaseResponse.of(response);
    }

    /**
     * 编辑离线分析任务
     * @param request SopTaskUpdateRequest
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/offlineTask/update")
    public BaseResponse<Void> updateOfflineTask(@RequestBody @Valid SopTaskUpdateRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        String modifier = WebContextHolder.getUserAuthInfo().getUserName();
        sopTaskCfgService.updateOfflineTask(tenantId, request, modifier);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 更新离线分析任务状态
     * @param id  任务Id
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/offlineTask/updateStatus")
    public BaseResponse<Void> updateOfflineTaskStatus(@RequestBody Long id) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        String modifier = WebContextHolder.getUserAuthInfo().getUserName();
        

        sopTaskCfgService.updateTaskStatus(tenantId, id, SopTaskCfgStatusEnum.RUNNING.getCode(), modifier);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 删除离线分析任务（支持取消运行中的任务）
     * @param taskId 任务ID
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/offlineTask/delete")
    public BaseResponse<Void> deleteOfflineTask(@RequestBody Long taskId) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        sopTaskCfgService.deleteOfflineTask(tenantId, taskId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }



    /**
     * 分析任务预计完成执行时间
     * @param request SopTaskCreateRequest
     * @return BaseResponse<Long>
     */
    @PostMapping("/w/offlineTask/completionTime")
    public BaseResponse<SopAnalysisCompletionTimeResponse> completionTime(@RequestBody @Valid SopTaskCreateRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        SopAnalysisCompletionTimeResponse response =
                sopTaskCfgService.getTaskCompletionTime(tenantId, request);
        return BaseResponse.of(response);
    }

    // ================= SOP知识引用分析相关接口 =================

    /**
     * 分页查询SOP知识引用分析结果
     * @param request SopAnalysisResultQueryRequest
     * @return BasePageResponse<SopAnalysisResultResponse>
     */
    @PostMapping("/r/analysis/results/query")
    public BasePageResponse.Page<SopAnalysisResultResponse> queryAnalysisResults(
            @RequestBody @Valid SopAnalysisResultQueryRequest request) {
        
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        log.info("分页查询SOP知识引用分析结果, tenantId: {}, request: {}", tenantId, request);
        
        try {
            return sopAnalysisResultService.queryAnalysisResults(tenantId, request);
            
        } catch (Exception e) {
            log.error("查询SOP知识引用分析结果失败, tenantId: {}, request: {}", tenantId, request, e);
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "查询失败");
        }
    }


    /**
     * 查询SOP知识引用分析Top结果
     * @param request SopAnalysisResultQueryRequest
     * @return BaseResponse<SopAnalysisResultAggResponse>
     */
    @PostMapping("/r/analysis/top/result")
    public BaseResponse<SopAnalysisResultAggResponse> queryTopResult(
            @RequestBody @Valid SopAnalysisResultQueryRequest request) {

        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        log.debug("查询SOP知识引用分析Top结果, tenantId: {}, request: {}", tenantId, request);

        try {
            return sopAnalysisResultService.queryAnalysisTopResults(tenantId, request);

        } catch (Exception e) {
            log.error("查询SOP知识引用分析Top结果, tenantId: {}, request: {}", tenantId, request, e);
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "查询失败");
        }
    }
}
