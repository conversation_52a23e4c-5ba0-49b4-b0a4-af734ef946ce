package com.baidu.keyue.deepsight.open.aiob.service;

import com.baidu.keyue.deepsight.models.idmapping.dto.DatasetKafkaMsgDTO;
import com.baidu.keyue.deepsight.open.aiob.utils.SessionAggKafkaMsgBuilder;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Kafka消息发送服务
 * 封装小秘书识别结果的Kafka消息发送逻辑
 *
 * @date 2025/8/19 17:50
 */
@Service
@Slf4j
public class SessionAggKafkaMsgService {
    
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    
    @Value("${deepSight.kafka.topic.aiob-session-agg}")
    private String aiobSessionTopic;
    
    /**
     * 批量发送会话数据的Kafka消息
     * 
     * @param sessionDataList 会话数据列表
     * @param tenantId 租户ID
     */
    public void sendBatchMessages(List<Map<String, Object>> sessionDataList, String tenantId) {
        if (sessionDataList == null || sessionDataList.isEmpty()) {
            log.warn("Session data list is empty, skip sending Kafka messages");
            return;
        }
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        
        log.info("Start sending batch Kafka messages, count: {}, tenantId: {}, topic: {}", 
            sessionDataList.size(), tenantId, aiobSessionTopic);
        
        for (Map<String, Object> sessionData : sessionDataList) {
            try {
                DatasetKafkaMsgDTO kafkaMsg = SessionAggKafkaMsgBuilder.buildSessionMessage(sessionData, tenantId);
                String messageJson = JsonUtils.toJsonWithOutException(kafkaMsg);
                
                kafkaTemplate.send(aiobSessionTopic, messageJson)
                    .whenComplete((result, ex) -> {
                        if (ex != null) {
                            failureCount.incrementAndGet();
                            log.error("Failed to send Kafka message for session: {}, error: {}", 
                                sessionData.get("sessionId"), ex.getMessage(), ex);
                        } else {
                            successCount.incrementAndGet();
                            log.debug("Kafka message sent successfully for session: {}, topic: {}, partition: {}, offset: {}", 
                                sessionData.get("sessionId"),
                                result.getRecordMetadata().topic(),
                                result.getRecordMetadata().partition(),
                                result.getRecordMetadata().offset());
                        }
                    });
                    
            } catch (Exception e) {
                failureCount.incrementAndGet();
                log.error("Failed to build or send Kafka message for session: {}, error: {}", 
                    sessionData.get("sessionId"), e.getMessage(), e);
            }
        }
        
        log.info("Batch Kafka message sending completed, tenantId: {}, total: {}, success: {}, failure: {}", 
            tenantId, sessionDataList.size(), successCount.get(), failureCount.get());
    }
    
    /**
     * 发送单个会话数据的Kafka消息
     * 
     * @param sessionData 会话数据
     * @param tenantId 租户ID
     * @return 是否发送成功
     */
    public boolean sendSingleMessage(Map<String, Object> sessionData, String tenantId) {
        try {
            DatasetKafkaMsgDTO kafkaMsg = SessionAggKafkaMsgBuilder.buildSessionMessage(sessionData, tenantId);
            String messageJson = JsonUtils.toJsonWithOutException(kafkaMsg);
            
            kafkaTemplate.send(aiobSessionTopic, messageJson)
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        log.error("Failed to send single Kafka message for session: {}, error: {}", 
                            sessionData.get("sessionId"), ex.getMessage(), ex);
                    } else {
                        log.debug("Single Kafka message sent successfully for session: {}", 
                            sessionData.get("sessionId"));
                    }
                });
            
            return true;
        } catch (Exception e) {
            log.error("Failed to send single Kafka message for session: {}, error: {}", 
                sessionData.get("sessionId"), e.getMessage(), e);
            return false;
        }
    }
}
