package com.baidu.keyue.deepsight.open.aiob.controller;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.open.aiob.model.dto.OpenAssistantRecDTO;
import com.baidu.keyue.deepsight.open.aiob.model.vo.OpenAssistantRecVO;
import com.baidu.keyue.deepsight.open.aiob.service.OpenAssistantRecognizeService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 外呼场景，小秘书识别open接口
 * 对某租户下的未识别会话进行小秘书识别
 *
 * @date 2025/8/19 11:51
 */
@RestController
@RequestMapping("/open/deepsight/v1/aiob/assistant")
@Slf4j
public class OpenAssistantRecognizeController {

    @Resource
    private OpenAssistantRecognizeService openAssistantRecognizeService;

    /**
     * 租户小秘书识别接口
     * 手动触发该租户下会话的小秘书识别
     *
     * @param request 请求参数
     * @return 识别结果统计
     */
    @PostMapping("/recognize")
    public BaseResponse<OpenAssistantRecVO> recognizeAssistant(@Valid @RequestBody OpenAssistantRecDTO request) {
        log.info("start assistant recognize request, tenantId: {}, startDate: {}",
            request.getTenantId(), request.getStartDate());

        OpenAssistantRecVO result = openAssistantRecognizeService.recognizeAssistant(
            request.getTenantId(), request.getStartDate());
        log.info("assistant recognize completed, tenantId: {}, result: {}", request.getTenantId(), result);
        return BaseResponse.of(result);
    }

    @PostMapping("/metric")
    public BaseResponse<Void> metricAggByTenant(@Valid @RequestBody OpenAssistantRecDTO request) {
        log.info("start assistant metric, tenantId: {}", request.getTenantId());
        openAssistantRecognizeService.metricAggByTenant(request.getTenantId());
        log.info("assistant metric completed, tenantId: {}", request.getTenantId());
        return BaseResponse.of(ErrorCode.SUCCESS);
    }
}
