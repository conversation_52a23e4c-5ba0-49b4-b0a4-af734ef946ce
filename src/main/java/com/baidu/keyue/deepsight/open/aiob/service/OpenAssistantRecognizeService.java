package com.baidu.keyue.deepsight.open.aiob.service;

import com.baidu.keyue.deepsight.open.aiob.model.vo.OpenAssistantRecVO;

import java.time.LocalDate;

/**
 * 小秘书识别open接口
 *
 * @date 2025/8/19 11:51
 */
public interface OpenAssistantRecognizeService {

    /**
     * 租户session的小秘书识别
     * @param tenantId 租户ID
     * @param startDate 起始日期
     * @return 识别结果
     */
    OpenAssistantRecVO recognizeAssistant(String tenantId, LocalDate startDate);

    /**
     * 手动触发聚合agg表计算
     * @param tenantId
     */
    void metricAggByTenant(String tenantId);
}
