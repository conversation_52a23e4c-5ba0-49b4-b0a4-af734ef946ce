package com.baidu.keyue.deepsight.open.aiob.service.impl;

import com.baidu.keyue.deepsight.config.AiobMetricAggConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.open.aiob.model.vo.OpenAssistantRecVO;
import com.baidu.keyue.deepsight.open.aiob.service.SessionAggKafkaMsgService;
import com.baidu.keyue.deepsight.open.aiob.service.OpenAssistantRecognizeService;
import com.baidu.keyue.deepsight.service.agg.AiobSessionMetricAggService;
import com.baidu.keyue.deepsight.service.ai.AssistantRecognitionService;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OpenAssistantRecognizeServiceImpl implements OpenAssistantRecognizeService {

    @Resource
    private DorisService dorisService;

    @Resource
    private AssistantRecognitionService assistantRecognitionService;

    @Resource
    private SessionAggKafkaMsgService kafkaMessageService;

    @Resource
    private AiobSessionMetricAggService aiobSessionMetricAggService;

    @Resource
    private AiobMetricAggConfiguration aiobAggConfiguration;

    /**
     * 根据租户ID识别小秘书会话。
     *
     * @param tenantId 租户ID
     * @param startDate 起始日期
     * @return OpenAssistantRecVO对象，包含准备会话数和助手会话数
     * @throws DeepSightException.DorisExecException 如果从Doris查询会话失败，则抛出此异常
     */
    @Override
    public OpenAssistantRecVO recognizeAssistant(String tenantId, LocalDate startDate) {
        // 处理默认日期逻辑
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(aiobAggConfiguration.getDay());
        }

        log.info("start assistant recognize, tenantId: {}, startDate: {}", tenantId, startDate);

        // 1. 获取该租户下的所有session（现在包含更多字段）
        List<Map<String, Object>> resultList;
        try {
            String sql = ORMUtils.generateSessionContentQueryForAssistant(tenantId, startDate);
            log.debug("query sql: {}", sql);
            resultList = dorisService.selectList(sql);
            log.info("query session count: {}, tenantId: {}", resultList.size(), tenantId);
        } catch (Exception e) {
            log.error("query session from doris failed, tenantId: {}", tenantId, e);
            throw new DeepSightException.DorisExecException("query session failed: " + e.getMessage());
        }

        // 2. 调用识别接口 + 收集需要更新的数据
        int prepareSessionCount = resultList.size();
        int assistantCount = 0;
        List<SessionUpdateInfo> updateInfoList = new ArrayList<>();
        List<Map<String, Object>> kafkaMessageDataList = new ArrayList<>();

        for (Map<String, Object> sessionData : resultList) {
            try {
                String sessionId = (String) sessionData.get("sessionId");
                String taskId = (String) sessionData.get("taskId");
                String conversationContent = (String) sessionData.get("conversationContent");

                log.debug("processing session: {}, taskId: {}", sessionId, taskId);

                // 调用小秘书识别接口
                Boolean recognizeResult = assistantRecognitionService.recognizeAssistant(
                    sessionId, taskId, conversationContent, tenantId);

                // 无论识别结果如何，都需要更新数据库和发送Kafka消息
                int isAutoAnswer = Boolean.TRUE.equals(recognizeResult) ? 1 : 0;

                if (Boolean.TRUE.equals(recognizeResult)) {
                    assistantCount++;
                    log.debug("session {} recognized as assistant", sessionId);
                } else {
                    log.debug("session {} recognized as non-assistant", sessionId);
                }

                // 收集更新信息
                updateInfoList.add(new SessionUpdateInfo(sessionId, isAutoAnswer));

                // 准备Kafka消息数据（包含完整字段）
                Map<String, Object> kafkaData = new HashMap<>(sessionData);
                kafkaData.put("is_auto_answer", isAutoAnswer);
                kafkaMessageDataList.add(kafkaData);

            } catch (Exception e) {
                log.error("process session failed, sessionData: {}", sessionData, e);
                // 继续处理下一个session，不中断整个流程
            }
        }

        // 3. 批量更新数据库
        if (!updateInfoList.isEmpty()) {
            batchUpdateSessionAutoAnswer(updateInfoList, tenantId);
        }

        // 4. 批量发送Kafka消息
        if (!kafkaMessageDataList.isEmpty()) {
            batchSendKafkaMessages(kafkaMessageDataList, tenantId);
        }

        // 5. 组装返回结果
        OpenAssistantRecVO result = OpenAssistantRecVO.builder()
            .prepareSessionCount(prepareSessionCount)
            .assistantCount(assistantCount)
            .build();

        log.info("assistant recognize completed, tenantId: {}, prepareSessionCount: {}, assistantCount: {}",
            tenantId, prepareSessionCount, assistantCount);

        return result;
    }

    /**
     * 手动触发聚合agg表计算（统计过去90天的数据）
     * @param tenantId
     */
    @Override
    public void metricAggByTenant(String tenantId) {
        String aggTableName = TenantUtils.generateAiobSessionAggTableName(tenantId);
        aiobSessionMetricAggService.aiobSessionMetricAggAllExec(aggTableName);
    }

    /**
     * 批量更新会话的is_auto_answer字段
     *
     * @param updateInfoList 需要更新的会话信息列表
     * @param tenantId 租户ID
     */
    private void batchUpdateSessionAutoAnswer(List<SessionUpdateInfo> updateInfoList, String tenantId) {
        try {
            String sessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);

            // 分别处理识别为小秘书和非小秘书的session
            List<String> assistantSessionIds = updateInfoList.stream()
                .filter(info -> info.isAutoAnswer == 1)
                .map(info -> info.sessionId)
                .collect(Collectors.toList());

            List<String> nonAssistantSessionIds = updateInfoList.stream()
                .filter(info -> info.isAutoAnswer == 0)
                .map(info -> info.sessionId)
                .collect(Collectors.toList());

            // 更新识别为小秘书的session
            if (!assistantSessionIds.isEmpty()) {
                String updateSql = ORMUtils.generateBatchUpdateAutoAnswerSQL(sessionTableName, assistantSessionIds, 1);
                log.debug("Batch update SQL for assistant sessions: {}", updateSql);
                dorisService.execSql(updateSql);
                log.info("Batch updated {} sessions to is_auto_answer=1, tenantId: {}",
                    assistantSessionIds.size(), tenantId);
            }

            // 更新识别为非小秘书的session
            if (!nonAssistantSessionIds.isEmpty()) {
                String updateSql = ORMUtils.generateBatchUpdateAutoAnswerSQL(sessionTableName, nonAssistantSessionIds, 0);
                log.debug("Batch update SQL for non-assistant sessions: {}", updateSql);
                dorisService.execSql(updateSql);
                log.info("Batch updated {} sessions to is_auto_answer=0, tenantId: {}",
                    nonAssistantSessionIds.size(), tenantId);
            }

        } catch (Exception e) {
            log.error("Batch update sessions failed, tenantId: {}, updateInfoList: {}",
                tenantId, updateInfoList, e);
            throw new DeepSightException.DorisExecException("Batch update sessions failed: " + e.getMessage());
        }
    }

    /**
     * 批量发送Kafka消息
     *
     * @param kafkaMessageDataList Kafka消息数据列表
     * @param tenantId 租户ID
     */
    private void batchSendKafkaMessages(List<Map<String, Object>> kafkaMessageDataList, String tenantId) {
        try {
            kafkaMessageService.sendBatchMessages(kafkaMessageDataList, tenantId);
            log.info("Batch sent {} Kafka messages for tenant: {}", kafkaMessageDataList.size(), tenantId);
        } catch (Exception e) {
            log.error("Batch send Kafka messages failed, tenantId: {}", tenantId, e);
            // Kafka发送失败不影响主流程，只记录错误
        }
    }

    /**
     * 会话更新信息内部类
     */
    private static class SessionUpdateInfo {
        private final String sessionId;
        private final int isAutoAnswer;

        public SessionUpdateInfo(String sessionId, int isAutoAnswer) {
            this.sessionId = sessionId;
            this.isAutoAnswer = isAutoAnswer;
        }

        @Override
        public String toString() {
            return "SessionUpdateInfo{" +
                "sessionId='" + sessionId + '\'' +
                ", isAutoAnswer=" + isAutoAnswer +
                '}';
        }
    }
}
