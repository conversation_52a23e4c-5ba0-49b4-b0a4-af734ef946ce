package com.baidu.keyue.deepsight.open.aiob.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会话更新数据传输对象
 * 用于批量更新会话的小秘书识别结果
 *
 * @date 2025/8/19 17:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SessionUpdateDTO {
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 是否为小秘书
     */
    private Boolean isAutoAnswer;
    
    /**
     * 租户ID
     */
    private String tenantId;
}
