package com.baidu.keyue.deepsight.open.aiob.utils;

import com.baidu.keyue.deepsight.models.idmapping.dto.DatasetKafkaMsgDTO;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * Kafka消息构建工具类
 * 用于构建小秘书识别结果的Kafka消息
 *
 * @date 2025/8/19 17:45
 */
@Component
@Slf4j
public class SessionAggKafkaMsgBuilder {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 构建会话数据的Kafka消息
     * 
     * @param sessionData 会话数据
     * @param tenantId 租户ID
     * @return Kafka消息对象
     */
    public static DatasetKafkaMsgDTO buildSessionMessage(Map<String, Object> sessionData, String tenantId) {
        DatasetKafkaMsgDTO kafkaMsg = new DatasetKafkaMsgDTO();
        
        // 设置表名
        String sessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        kafkaMsg.setCode(sessionTableName);
        
        // 构建数据Map，只包含metricCalculate需要的字段
        Map<String, Object> data = new HashMap<>();
        
        // 必需字段
        data.put("oneId", sessionData.get("oneId"));
        data.put("talkingTurn", sessionData.get("talkingTurn"));
        data.put("durationTimeLen", sessionData.get("durationTimeLen"));
        data.put("action", sessionData.get("action"));
        data.put("is_auto_answer", 1); // 设置为小秘书
        
        // 处理时间字段格式化
        Object startTimeObj = sessionData.get("startTime");
        if (startTimeObj != null) {
            String startTimeStr;
            if (startTimeObj instanceof LocalDateTime) {
                startTimeStr = ((LocalDateTime) startTimeObj).format(FORMATTER);
            } else {
                startTimeStr = String.valueOf(startTimeObj);
            }
            data.put("startTime", startTimeStr);
        }
        
        kafkaMsg.setData(data);
        
        log.debug("Built Kafka message for session: {}, table: {}", 
            sessionData.get("sessionId"), sessionTableName);
        
        return kafkaMsg;
    }
}
