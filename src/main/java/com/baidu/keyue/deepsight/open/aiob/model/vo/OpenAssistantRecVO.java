package com.baidu.keyue.deepsight.open.aiob.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class OpenAssistantRecVO {

    /**
     * 满足初步筛选条件，调用识别接口的Session数量
     */
    private Integer prepareSessionCount;

    /**
     * 小秘书识别为true的数量
     */
    private Integer assistantCount;
}
